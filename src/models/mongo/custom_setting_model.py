#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 03/07/2025
"""
from src.models.mongo import MongoBaseModel


class CustomSettingModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "custom_setting"

    async def get_custom_setting_role(self):
        query = {"type": "role_user"}
        result = await self.find_one(query)
        if result:
            return result.get("config", {})
        return {}

    async def get_custom_setting_role_admin_sync(self):
        query = {"type": "set_role_admin_sync"}
        result = await self.find_one(query)
        if result:
            return result.get("config", {})
        return {}
