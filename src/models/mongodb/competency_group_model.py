#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

from bson import ObjectId

from src.common.common import CommonKey, CompetencyGroupKey
from src.models.mongo import MongoBaseModel
from src.utils.time_helper import get_time_now


class CompetencyGroupModel(MongoBaseModel):
    """
    Collection name: competency_group
    Note: <PERSON><PERSON>u trữ nhóm năng lực
    """

    def __init__(self):
        super().__init__()
        self.collection_name = "competency_group"

    async def add_competency_group(self, payload):
        document = await self.insert_document(payload)
        return document.inserted_id

    async def get_detail_competency_group_by_id(self, competency_group_id):
        return await self.find_one({CompetencyGroupKey.ID: ObjectId(competency_group_id)})

    async def update_competency_group(self, competency_group_id, payload):
        return await self.update_dictionary(ObjectId(competency_group_id), payload)

    async def get_list_competency_group_by_condition(self, condition, sort, order, page, per_page):
        results = await self.find_paginate(
            condition,
            page=page,
            per_page=per_page,
            sort=sort,
            order=order,
            projection={CompetencyGroupKey.LOWER_CASE_NAME: 0},
        )

        total_count = await self.count(condition)

        return results, total_count

    async def get_detail_competency_group_by_name(self, name):
        return await self.find_one({CompetencyGroupKey.LOWER_CASE_NAME: name})

    async def get_detail_competency_group_by_id(self, competency_group_id):
        return await self.find_one({CompetencyGroupKey.ID: ObjectId(competency_group_id)})

    async def get_detail_competency_group_default_by_id(self, competency_group_id):
        return await self.find_one(
            {CompetencyGroupKey.ID: ObjectId(competency_group_id), CompetencyGroupKey.IS_DEFAULT: True}
        )

    async def delete_competency_group_by_id(self, competency_group_id):
        return await self.delete_one({"_id": ObjectId(competency_group_id)})

    async def get_list_competency_group_default(self):
        lst_competency_group = await self.find({CompetencyGroupKey.IS_DEFAULT: True})
        return lst_competency_group

    async def init_competency_default(self, email):
        data_init: list = [
            {
                CompetencyGroupKey.NAME: "Nhóm năng lực cốt lõi",
                CompetencyGroupKey.LOWER_CASE_NAME: "nhóm năng lực cốt lõi",
                CompetencyGroupKey.DESCRIPTION: "",
                CompetencyGroupKey.IS_DEFAULT: True,
                CommonKey.CREATED_BY: email,
                CommonKey.UPDATED_BY: email,
                CommonKey.CREATED_TIME: get_time_now(),
                CommonKey.UPDATED_TIME: get_time_now(),
                CompetencyGroupKey.ORDER: 1,
            },
            {
                CompetencyGroupKey.NAME: "Nhóm năng lực chuyên môn",
                CompetencyGroupKey.LOWER_CASE_NAME: "nhóm năng lực chuyên môn",
                CompetencyGroupKey.DESCRIPTION: "",
                CompetencyGroupKey.IS_DEFAULT: True,
                CommonKey.CREATED_BY: email,
                CommonKey.UPDATED_BY: email,
                CommonKey.CREATED_TIME: get_time_now(),
                CommonKey.UPDATED_TIME: get_time_now(),
                CompetencyGroupKey.ORDER: 2,
            },
            {
                CompetencyGroupKey.NAME: "Nhóm năng lực bổ trợ",
                CompetencyGroupKey.LOWER_CASE_NAME: "nhóm năng lực bổ trợ",
                CompetencyGroupKey.DESCRIPTION: "",
                CompetencyGroupKey.IS_DEFAULT: True,
                CommonKey.CREATED_BY: email,
                CommonKey.UPDATED_BY: email,
                CommonKey.CREATED_TIME: get_time_now(),
                CommonKey.UPDATED_TIME: get_time_now(),
                CompetencyGroupKey.ORDER: 3,
            },
            {
                CompetencyGroupKey.NAME: "Nhóm năng lực quản lý",
                CompetencyGroupKey.LOWER_CASE_NAME: "nhóm năng lực quản lý",
                CompetencyGroupKey.DESCRIPTION: "",
                CompetencyGroupKey.IS_DEFAULT: True,
                CommonKey.CREATED_BY: email,
                CommonKey.UPDATED_BY: email,
                CommonKey.CREATED_TIME: get_time_now(),
                CommonKey.UPDATED_TIME: get_time_now(),
                CompetencyGroupKey.ORDER: 4,
            },
        ]
        await self.insert_many(data_init)
        return data_init

    async def get_list_competency_group_by_ids(self, competency_group_ids):
        competency_group_ids = [ObjectId(competency_group_id) for competency_group_id in competency_group_ids]

        return await self.find({CompetencyGroupKey.ID: {"$in": competency_group_ids}})
