#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

import datetime
from typing import Any, Dict, Optional

from src.models.mongo import MongoBaseModel


class LogUserModel(MongoBaseModel):
    """
    Collection name: log_user
    Note: Lưu trữ thông tin log user access và activities
    """

    def __init__(self):
        super().__init__()
        self.collection_name = "log_user"

    async def log_admin_access(self, log_data) -> str:
        """
        Log admin user access for monitoring purposes

        Args:
            username: Username of the user
            path: API path accessed
            method: HTTP method (GET, POST, etc.)
            user_roles: List of user roles
            user_id: User ID
            ip_address: Client IP address
            user_agent: Client user agent
            additional_data: Additional data to log

        Returns:
            str: Inserted document ID
        """

        result = await self.insert(log_data)
        return str(result.inserted_id)

    async def get_admin_access_logs(
        self,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        username: Optional[str] = None,
        path: Optional[str] = None,
        page: int = 1,
        per_page: int = 50,
    ) -> Dict[str, Any]:
        """
        Retrieve admin access logs with filtering

        Args:
            start_date: Start date filter
            end_date: End date filter
            username: Username filter
            path: Path filter
            page: Page number
            per_page: Items per page

        Returns:
            Dict containing logs and pagination info
        """
        search_options = {"is_admin": True}

        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter["$gte"] = start_date
            if end_date:
                date_filter["$lte"] = end_date
            search_options["datetime"] = date_filter

        if username:
            search_options["username"] = {"$regex": username, "$options": "i"}

        if path:
            search_options["path"] = {"$regex": path, "$options": "i"}

        # Get total count
        total_count = await self.count(search_options)

        # Get paginated results
        cursor = await self.find_paginate(
            search_options, page=page, per_page=per_page, sort_option=[("datetime", -1)]  # Sort by newest first
        )

        logs = []
        async for log in cursor:
            log["_id"] = str(log["_id"])
            logs.append(log)

        return {
            "logs": logs,
            "total_count": total_count,
            "page": page,
            "per_page": per_page,
            "total_pages": (total_count + per_page - 1) // per_page,
        }

    async def get_user_activity_stats(
        self,
        username: Optional[str] = None,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
    ) -> Dict[str, Any]:
        """
        Get user activity statistics

        Args:
            username: Username filter
            start_date: Start date filter
            end_date: End date filter

        Returns:
            Dict containing activity statistics
        """
        match_stage = {}

        if username:
            match_stage["username"] = username

        if start_date or end_date:
            date_filter = {}
            if start_date:
                date_filter["$gte"] = start_date
            if end_date:
                date_filter["$lte"] = end_date
            match_stage["datetime"] = date_filter

        pipeline = [
            {"$match": match_stage},
            {
                "$group": {
                    "_id": {"username": "$username", "is_admin": "$is_admin"},
                    "total_requests": {"$sum": 1},
                    "unique_paths": {"$addToSet": "$path"},
                    "last_access": {"$max": "$datetime"},
                    "first_access": {"$min": "$datetime"},
                }
            },
            {
                "$project": {
                    "username": "$_id.username",
                    "is_admin": "$_id.is_admin",
                    "total_requests": 1,
                    "unique_paths_count": {"$size": "$unique_paths"},
                    "unique_paths": 1,
                    "last_access": 1,
                    "first_access": 1,
                    "_id": 0,
                }
            },
            {"$sort": {"total_requests": -1}},
        ]

        cursor = await self.aggregate(pipeline)
        stats = []
        async for stat in cursor:
            stats.append(stat)

        return {"user_statistics": stats}
