#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 17/08/2025
"""

import re
import uuid

from bson.objectid import ObjectId
from pymongo import MongoClient

from configs import ApplicationConfig

base_client = MongoClient(ApplicationConfig.LADDER_MONGO_URI)
db_name = re.search(r"^mongodb://[^@]+@[^/]+/([^?$]+).*$", ApplicationConfig.LADDER_MONGO_URI).group(1)
# db_name = "ladder"


class MongoBaseModel:
    client = base_client
    collection_name = ""

    @property
    def collection(self):
        db = self.client[db_name]
        return db[self.collection_name]

    async def _db(self):
        return self.client[db_name]

    async def get_db(self):
        db = self.client[db_name]
        collection = db[self.collection_name]
        return collection

    async def insert(self, dictionary):
        db = self.client[db_name]
        return db[self.collection_name].insert_one(dictionary)

    async def insert_many(self, document):
        db = self.client[db_name]
        return db[self.collection_name].insert_many(document)

    async def insert_document(self, dictionary):
        db = self.client[db_name]
        return db[self.collection_name].insert_one(dictionary)

    async def update_set_dictionary(self, search_option, dictionary):
        db = self.client[db_name]

        document = db[self.collection_name].find_one(search_option)
        if document:
            return (
                db[self.collection_name]
                .update_one(filter=search_option, update={"$set": dictionary}, upsert=True)
                .matched_count
                >= 1
            )
        return None

    async def update_dictionary(self, document_id, dictionary):
        if isinstance(document_id, str):
            document_id = ObjectId(document_id)
        return (
            self.client.get_database(db_name)[self.collection_name]
            .update_one({"_id": document_id}, {"$set": dictionary})
            .matched_count
        )

    async def update_one_query(self, query, data):
        return self.client.get_database(db_name)[self.collection_name].update_one(query, {"$set": data}).matched_count

    async def update_many(self, filter_option, update_option):
        db = self.client[db_name]
        return db[self.collection_name].update_many(filter_option, {"$set": update_option}).matched_count

    async def update(self, filter_option, update_option, upsert=False, multi=False):
        db = self.client[db_name]
        db[self.collection_name].update_one(filter_option, update_option, upsert=upsert, multi=multi)

    async def update_by_set(self, filter_option, update_option, upsert=False, multi=False):
        db = self.client[db_name]
        return db[self.collection_name].update_one(filter_option, {"$set": update_option}, upsert=upsert)

    async def delete_one(self, delete_options):
        db = self.client[db_name]
        return db[self.collection_name].delete_one(delete_options)

    async def delete_many(self, delete_options):
        db = self.client[db_name]
        return db[self.collection_name].delete_many(delete_options).deleted_count

    async def upsert(self, search_option, dictionary):
        db = self.client[db_name]
        document = db[self.collection_name].find_one(search_option)
        if document:
            document.update(dictionary)
            self.collector().replace_one({"_id": document.get("_id")}, dictionary, upsert=True)
            return document.get("_id")
        else:
            return db[self.collection_name].insert_one(dictionary).inserted_id

    async def find(self, search_option, obj_field_select: dict = None):
        db = self.client[db_name]
        if obj_field_select:
            return db[self.collection_name].find(search_option, obj_field_select)
        return db[self.collection_name].find(search_option)

    async def find_one(self, search_option, projection={}):
        db = self.client[db_name]
        return db[self.collection_name].find_one(search_option, projection)

    async def find_one_test(self, search_option, projection={}):
        db = self.client[db_name]
        return await db[self.collection_name].find_one(search_option, projection)

    async def collector(self):
        return self._db()[self.collection_name]

    async def count_by_query(self, count_option):
        db = self.client[db_name]
        return db[self.collection_name].count_documents(count_option)

    async def count(self, search_option=None):
        db = self.client[db_name]
        if not search_option:
            search_option = {}
        return db[self.collection_name].count_documents(search_option)

    async def select_all(self, search_option, projection=None):
        return self.collector().find(search_option, projection)

    async def find_paginate(self, search_option, page=0, per_page=None, sort=None, order=None, projection=None):
        db = self.client[db_name]
        collection = db[self.collection_name].find(search_option, projection)
        if sort:
            collection = collection.sort(sort, order)

        if page != -1:
            if per_page:
                collection = collection.limit(per_page)
            if page > 0:
                page -= 1
                offset = int(page) * int(per_page)
                collection = collection.skip(offset)

        return collection

    async def find_paginate_version_new(self, search_option, page=0, per_page=None, sort_option=None, projection=None):
        db = self.client[db_name]
        collection = db[self.collection_name].find(search_option, projection)
        if sort_option:
            collection = collection.sort(sort_option)

        if page != -1:
            if per_page:
                collection = collection.limit(per_page)
            if page > 0:
                page -= 1
                offset = int(page) * int(per_page)
                collection = collection.skip(offset)

        return collection

    async def _aggregate(self, group, match: object, sort=None, project=None):
        db = self.client[db_name]
        pipeline = []
        if match:
            pipeline.append({"$match": match})
        pipeline.append({"$group": group})
        if sort:
            pipeline.append({"$sort": sort})
        if project:
            pipeline.append({"$project": project})
        return db[self.collection_name].aggregate(pipeline)

    async def aggregate(self, pipeline: list):
        db = self.client[db_name]
        return db[self.collection_name].aggregate(pipeline)

    async def distinct(self, fields, query):
        db = self.client[db_name]

        if type(fields) is str:
            return db[self.collection_name].distinct(fields, query)

        return None

    @staticmethod
    async def normalize_uuid(some_uuid):
        if isinstance(some_uuid, str):
            return uuid.UUID(some_uuid)
        return some_uuid

    @staticmethod
    async def normalize_object_id(some_object_id):
        if isinstance(some_object_id, str):
            return ObjectId(some_object_id)
        return some_object_id
