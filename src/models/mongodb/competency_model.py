#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

from bson import ObjectId

from src.common.common import CompetencyKey
from src.models.mongo import MongoBaseModel


class CompetencyModel(MongoBaseModel):
    """
    Collection name: competency
    Note: <PERSON><PERSON>u trữ năng lực trong từ điển năng lực
    """

    def __init__(self):
        super().__init__()
        self.collection_name = "competency"

    async def get_list_competency_by_condition(self, condition, sort="updated_time", order=-1, page=1, per_page=20):
        results = await self.find_paginate(
            condition,
            page=page,
            per_page=per_page,
            sort=sort,
            order=order,
        )

        total_count = await self.count(condition)

        return results, total_count

    async def get_detail_competency_by_name(self, name):
        return await self.find_one({CompetencyKey.LOWER_CASE_NAME: name})

    async def get_detail_competency_by_id(self, competency_id):
        return await self.find_one({CompetencyKey.ID: ObjectId(competency_id)})

    async def add_competency(self, competency):
        document = await self.insert_document(competency)
        return document.inserted_id

    async def update_competency(self, competency_id, payload):
        return await self.update_dictionary(ObjectId(competency_id), payload)

    async def delete_competencies_by_ids(self, competency_ids):
        competency_ids = [ObjectId(competency_id) for competency_id in competency_ids]
        return await self.delete_many({CompetencyKey.ID: {"$in": competency_ids}})

    async def get_list_competencies_by_group_id(self, group_id):
        return await self.find(
            {CompetencyKey.COMPETENCY_GROUP_ID: group_id},
            obj_field_select={CompetencyKey.LOWER_CASE_NAME: 0, CompetencyKey.COMPETENCY_GROUP_ID: 0},
        )

    async def update_competencies_by_ids(self, competency_ids, payload):
        competency_ids = [ObjectId(competency_id) for competency_id in competency_ids]
        return await self.update_many({CompetencyKey.ID: {"$in": competency_ids}}, payload)

    async def get_list_competencies_by_department_id(self, company_id, department_id):
        return await self.find(
            await self.find(
                {
                    "company_id": company_id,
                    "default_apply_department_ids": {"$in": [department_id]},
                    "is_default_apply": True,
                }
            )
        )
