#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

from bson import ObjectId

from src.common.choices import (
    CompetencyFrameworkStatusChoice,
    TypeCompetencyFrameworkChoice,
)
from src.common.common import CommonKey, CompetencyFrameworkKey
from src.models.mongo import MongoBaseModel
from src.utils.time_helper import get_time_now


class CompetencyFrameworkModel(MongoBaseModel):
    """
    Collection name: competency_framework
    Note: L<PERSON>u trữ nhóm năng lực do các Leader tạo
    """

    def __init__(self):
        super().__init__()
        self.collection_name = "competency_framework"

    async def insert_competency_framework(self, payload):
        payload.update({CompetencyFrameworkKey.LOWER_CASE_NAME: payload["name"].lower()})

        document = await self.insert_document(payload)
        return document.inserted_id

    async def add_competency_framework(self, payload):
        document = await self.insert_document(payload)
        return document.inserted_id

    async def get_detail_competency_framework_by_id(self, competency_framework_id):
        return await self.find_one({CompetencyFrameworkKey.ID: ObjectId(competency_framework_id)})

    async def update_competency_framework(self, competency_framework_id, payload):
        if payload.get("name"):
            payload.update({CompetencyFrameworkKey.LOWER_CASE_NAME: payload["name"].lower()})

        return await self.update_dictionary(ObjectId(competency_framework_id), payload)

    async def update_competency_framework_by_id(self, competency_framework_id, payload):
        return await self.update_dictionary(ObjectId(competency_framework_id), payload)

    async def delete_competency_framework(self, competency_framework_id):
        return await self.update_by_set(
            {CompetencyFrameworkKey.ID: ObjectId(competency_framework_id)},
            {
                CompetencyFrameworkKey.TYPE: TypeCompetencyFrameworkChoice.DRAFT_DELETE.value,
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT_DELETE.value,
            },
        )

    async def delete_competency_framework_by_id(self, competency_framework_id):
        return await self.update_by_set(
            {CompetencyFrameworkKey.ID: ObjectId(competency_framework_id)},
            {
                CompetencyFrameworkKey.TYPE: TypeCompetencyFrameworkChoice.DRAFT_DELETE.value,
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.DRAFT_DELETE.value,
            },
        )

    async def get_list_competency_framework_by_condition(
        self, condition, sort="updated_time", order=-1, page=1, per_page=20
    ):
        results = await self.find_paginate(
            condition,
            page=page,
            per_page=per_page,
            sort=sort,
            order=order,
        )

        total_count = await self.count(condition)

        return results, {
            CommonKey.PAGE: page,
            CommonKey.PER_PAGE: per_page,
            CommonKey.TOTAL_COUNT: total_count,
            CommonKey.TOTAL_PAGE: total_count // per_page + 1,
        }

    async def get_detail_competency_framework_by_name(self, name):
        return await self.find_one({CompetencyFrameworkKey.LOWER_CASE_NAME: name.lower()})

    async def count_competency_frameworks_by_condition(self, filter_options):
        return await self.count_by_query(filter_options)

    async def get_list_competency_frameworks_expired(self):
        results = await self.find(
            {
                CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
                CompetencyFrameworkKey.END_TIME: {"$lt": get_time_now()},
            }
        )

        return results

    async def get_competency_frameworks_of_exist_job_title(self, company_id, job_title_ids, lst_id_ignore=None):
        filter_option = {
            CommonKey.COMPANY_ID: company_id,
            CompetencyFrameworkKey.JOB_TITLE_IDS: {"$elemMatch": {"$in": job_title_ids}},
            CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
        }
        if lst_id_ignore:
            filter_option.update({"_id": {"$nin": [ObjectId(id_ignore) for id_ignore in lst_id_ignore]}})
        results = await self.find(filter_option)

        return list(results)

    async def get_list_competency_frameworks_by_job_title_ids_diff_cf_current(self, job_title_ids, cf_current_id):
        filter_option = {
            CompetencyFrameworkKey.JOB_TITLE_IDS: {"$in": job_title_ids},
            CompetencyFrameworkKey.STATUS: CompetencyFrameworkStatusChoice.ACTIVE.value,
        }
        if cf_current_id:
            filter_option.update({"_id": {"$ne": ObjectId(cf_current_id)}})
        return await self.find(
            filter_option,
            {CompetencyFrameworkKey.START_TIME: 1, CompetencyFrameworkKey.END_TIME: 1, CompetencyFrameworkKey.NAME: 1},
        )
