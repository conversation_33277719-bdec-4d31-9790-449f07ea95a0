#!/usr/bin/env python
# -*- coding: utf-8 -*-

from src.models.mongo import MongoBaseModel


class TaskPerformanceModel(MongoBaseModel):
    """
    Collection name: task_performance
    Note: <PERSON><PERSON><PERSON> trữ thông tin hiệu suất công việc
    """

    def __init__(self):
        super().__init__()
        self.collection_name = "task_performance"

    async def get_task_performance_by_user_id(self, user_id, **extra_search):
        search_option = {"user_id": user_id}
        if extra_search:
            search_option.update(extra_search)
        return await self.find_one(search_option)
