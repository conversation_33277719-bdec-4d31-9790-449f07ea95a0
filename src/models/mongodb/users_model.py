#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 19/08/2025
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: LocDX
    Company: MobioVN
    Date created: 19/08/2025
"""
from bson import ObjectId

from src.common.common import CompetencyKey
from src.models.mongo import MongoBaseModel


class UsersModel(MongoBaseModel):
    def __init__(self):
        super().__init__()
        self.collection_name = "users"
