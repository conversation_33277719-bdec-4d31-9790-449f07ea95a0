import asyncio
import datetime
import json

from mobio.libs.logging import <PERSON><PERSON>Logging
from sqlalchemy.orm import Session

from configs.database import get_db
from src.common.caching import lru_cache_m_caching
from src.common.common import CompanyKey, EmploymentTypeKey
from src.common.constants import ConstantStatus
from src.libs.lark_docs_sdk.oapi.object.company import LarkObjectCompanyApi
from src.libs.lark_docs_sdk.oapi.object.department import LarkObjectDepartmentApi
from src.libs.lark_docs_sdk.oapi.object.user import LarkObjectUserApi
from src.models.mongo.custom_setting_model import CustomSettingModel
from src.models.postgres.base_model import (
    CompanyModel,
    DepartmentModel,
    EmploymentTypeModel,
    JobTitleModel,
    RoleModel,
    UserDepartmentModel,
    UserModel,
    UserRoleModel,
)
from src.utils import utf8_to_ascii
from src.utils.time_helper import to_time_at

employee_types_init = [
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 1,
        EmploymentTypeKey.VI_NAME: "Chính thức",
        EmploymentTypeKey.EN_NAME: "Regular",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 2,
        EmploymentTypeKey.VI_NAME: "Thực tập sinh",
        EmploymentTypeKey.EN_NAME: "Intern",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 5,
        EmploymentTypeKey.VI_NAME: "Tư vấn",
        EmploymentTypeKey.EN_NAME: "Consultant",
    },
    {
        EmploymentTypeKey.EMPLOYMENT_TYPE_ID: 6,
        EmploymentTypeKey.VI_NAME: "Thử việc",
        EmploymentTypeKey.EN_NAME: "Probation",
    },
]


class HandlerSyncDataFromLark:

    class Locker:
        """
        Ensure only one job sync data from lark is running at the same time
        """

        exp = 3600
        key = f"{lru_cache_m_caching.cache_prefix}#sync_data_from_lark"

        @staticmethod
        def get_lock_data():
            lock_data_dumps = lru_cache_m_caching.cache._redis.get(HandlerSyncDataFromLark.Locker.key)
            if not lock_data_dumps:
                return {}
            lock_data = json.loads(lock_data_dumps)
            return lock_data

        @staticmethod
        def lock():
            if HandlerSyncDataFromLark.Locker.get_lock_data().get("is_locked", False):
                raise RuntimeError("Sync data from lark is running")

            time_now = datetime.datetime.now(datetime.UTC).timestamp()
            data = {
                "is_locked": True,
                "created_time": time_now,
                "exp_time": time_now + HandlerSyncDataFromLark.Locker.exp,
            }
            lock_data_dumps = json.dumps(data, default=str)
            lru_cache_m_caching.cache._redis.set(
                HandlerSyncDataFromLark.Locker.key, lock_data_dumps, ex=HandlerSyncDataFromLark.Locker.exp
            )

        @staticmethod
        def unlock():
            lru_cache_m_caching.cache._redis.delete(HandlerSyncDataFromLark.Locker.key)

    @staticmethod
    def sync_data_from_lark():
        session_db: Session = next(get_db())
        company_info_of_lark = LarkObjectCompanyApi().get_company_information()
        if company_info_of_lark:
            company_info_of_lark = company_info_of_lark["tenant"]
        MobioLogging().info(f"sync_data_from_lark::company_info_of_lark::{company_info_of_lark}")
        if not company_info_of_lark:
            MobioLogging().error(f"sync_data_from_lark::company_info_of_lark::error::company from lark is empty")
            return

        query_of_company = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark["tenant_key"])
        if query_of_company.first():
            query_of_company.update(
                {
                    CompanyKey.NAME: company_info_of_lark["name"],
                    CompanyKey.AVATAR: company_info_of_lark["avatar"]["avatar_origin"],
                }
            )
        else:
            session_db.add(
                CompanyModel(
                    tenant_key=company_info_of_lark["tenant_key"],
                    name=company_info_of_lark["name"],
                    avatar=company_info_of_lark["avatar"]["avatar_origin"],
                )
            )
        session_db.commit()

        company_info = session_db.query(CompanyModel).filter_by(tenant_key=company_info_of_lark["tenant_key"]).first()

        for employee_type in employee_types_init:
            employee_type_current = (
                session_db.query(EmploymentTypeModel)
                .filter_by(employment_type_id=employee_type[EmploymentTypeKey.EMPLOYMENT_TYPE_ID])
                .update(
                    {
                        EmploymentTypeKey.VI_NAME: employee_type[EmploymentTypeKey.VI_NAME],
                        EmploymentTypeKey.EN_NAME: employee_type[EmploymentTypeKey.EN_NAME],
                    }
                )
            )
            if not employee_type_current:
                session_db.add(
                    EmploymentTypeModel(
                        company_id=company_info.company_id,
                        employment_type_id=employee_type[EmploymentTypeKey.EMPLOYMENT_TYPE_ID],
                        vi_name=employee_type[EmploymentTypeKey.VI_NAME],
                        en_name=employee_type[EmploymentTypeKey.EN_NAME],
                    )
                )
            session_db.commit()

        departments = LarkObjectDepartmentApi().get_all_department_can_access()
        MobioLogging().info(f"sync_data_from_lark::departments::{departments}")
        if not departments:
            MobioLogging().error(f"sync_data_from_lark::departments::error::department from lark is empty")
            return
        child_departments = list()

        department_order = 0

        # Clear all relation user-department
        session_db.query(UserDepartmentModel).delete()
        session_db.query(UserRoleModel).delete()
        session_db.commit()

        admin_role = session_db.query(RoleModel).filter_by(lower_case_name="admin").first()
        leader_role = session_db.query(RoleModel).filter_by(lower_case_name="leader").first()
        user_role = session_db.query(RoleModel).filter_by(lower_case_name="user").first()

        mapping_department_id_with_data = {}
        mapping_user_id_with_data = {}

        mapping_user_c_level = {}
        department_id_level_c = []

        # insert information department
        for department_information in departments:
            MobioLogging().info(f"sync_data_from_lark::department_information::{department_information}")

            department_id = department_information["department_id"]
            department_order += 1
            mapping_department_id_with_data[department_id] = {**department_information, "order": department_order}

            if int(department_information["parent_department_id"]):
                child_departments.append(department_information)
                continue

            if department_information["name"].lower() in ["bom", "bod"]:
                department_information["name"] = "BOD"

            display = 1
            if department_information["name"].lower() in ["bom", "bod"]:
                display = 0

            status = 1 if not department_information["status"]["is_deleted"] else 0
            lower_case_name = department_information["name"].lower()
            department_current = (
                session_db.query(DepartmentModel)
                .filter_by(lark_department_id=department_information["department_id"])
                .first()
            )
            owners = department_information.get("owners", [])

            department_name = department_information["name"]
            if not department_current:
                department_current = DepartmentModel(
                    lark_department_id=department_information["department_id"],
                    company_id=company_info.company_id,
                    open_department_id=department_information["open_department_id"],
                    name=department_information["name"],
                    lower_case_name=lower_case_name,
                    status=status,
                    display=display,
                    order=department_order,
                    owners=owners,
                )
                session_db.add(department_current)
            else:
                department_current.status = status
                department_current.display = display
                department_current.lower_case_name = lower_case_name
                department_current.name = department_information["name"]
                department_current.owners = owners
            session_db.commit()
            if department_information["name"] in ["BOM", "BOD"]:
                department_id_level_c.append(department_id)
            mapping_department_id_with_data[department_id].update(
                {"mbo_department_id": department_current.department_id}
            )

            users = LarkObjectUserApi().get_list_all_users_of_department(department_id=department_id)
            if not users:
                continue

            for user_information in users:
                open_id = user_information["open_id"]
                if open_id not in mapping_user_id_with_data:
                    mapping_user_id_with_data[open_id] = user_information
                else:
                    mapping_user_id_with_data[open_id]["orders"].extend(user_information["orders"])
                if department_information["name"] in ["BOM", "BOD"]:
                    mapping_user_c_level[open_id] = user_information

        for open_id, user_information in mapping_user_id_with_data.items():
            lark_user_id = user_information["user_id"]
            department_orders = user_information["orders"]
            leader_id = user_information.get("leader_user_id") if open_id not in mapping_user_c_level else None

            for department_order in department_orders:
                department_id = department_order["department_id"]
                is_primary_dept = department_order["is_primary_dept"]

                if not is_primary_dept:
                    continue

                department_data = mapping_department_id_with_data.get(department_id)
                if department_data:
                    leaders = department_data.get("leaders")
                    main_personal_in_charge = False
                    personal_in_charge = False

                    if leaders:
                        for leader in leaders:
                            if leader["leaderID"] == lark_user_id:
                                if leader["leaderType"] == 1:
                                    main_personal_in_charge = True
                                if leader["leaderType"] == 2:
                                    personal_in_charge = True

                    if "mbo" not in mapping_user_id_with_data[open_id]:
                        mapping_user_id_with_data[open_id]["mbo"] = {}
                    mapping_user_id_with_data[open_id]["mbo"][department_id] = {
                        "mbo_leader_id": leader_id,
                        "department_id": department_id,
                        "mbo_department_id": department_data["mbo_department_id"],
                        "mbo_main_personal_in_charge": main_personal_in_charge,
                        "mbo_personal_in_charge": personal_in_charge,
                    }

        # MobioLogging().info(f"sync_data_from_lark::mapping_user_id_with_data::{mapping_user_id_with_data}")

        custom_setting_model: CustomSettingModel = CustomSettingModel()
        custom_setting_role_admin_sync = asyncio.run(custom_setting_model.get_custom_setting_role_admin_sync())

        for _, user_information in mapping_user_id_with_data.items():

            user_information_email = user_information.get("email")

            if "mbo" not in user_information:
                continue
            mbo_information_department = user_information["mbo"]

            open_user_id = user_information.get("open_id")
            MobioLogging().info(f"start process email {user_information_email}")

            mbo_main_personal_in_charge = False
            mbo_personal_in_charge = False

            lst_department = []
            lst_department_id = list(mbo_information_department.keys())

            for department_id, mbo_information in mbo_information_department.items():
                if mbo_information["mbo_main_personal_in_charge"]:
                    mbo_main_personal_in_charge = True
                if mbo_information["mbo_personal_in_charge"]:
                    mbo_personal_in_charge = True

            department_current = (
                session_db.query(DepartmentModel).filter_by(department_id=mbo_information["mbo_department_id"]).first()
            )

            lark_user_id = user_information["user_id"]
            user_status = (
                ConstantStatus.ACTIVE if user_information["status"]["is_activated"] else ConstantStatus.DEACTIVE
            )
            unsigned_name = utf8_to_ascii(user_information["name"]).lower()
            thumb_avatar_link = user_information["avatar"]["avatar_origin"]
            # Assign role
            user_information_roles = [user_role]
            if open_user_id in mapping_user_c_level:
                user_information_roles.append(admin_role)
            if mbo_main_personal_in_charge or mbo_personal_in_charge:
                user_information_roles.append(leader_role)
            if user_information_email in custom_setting_role_admin_sync:
                user_information_roles.append(admin_role)

            user_job_title = user_information["job_title"]
            lst_department = []
            for department_id in lst_department_id:

                department_current = (
                    session_db.query(DepartmentModel).filter_by(lark_department_id=department_id).first()
                )

                if department_current:
                    job_title = (
                        session_db.query(JobTitleModel)
                        .filter_by(name=user_job_title, department_id=department_current.department_id)
                        .first()
                    )
                    if not job_title:
                        job_title = JobTitleModel(
                            company_id=company_info.company_id,
                            name=user_information["job_title"],
                            lower_case_name=user_information["job_title"].lower(),
                            department_id=department_current.department_id,
                        )
                        session_db.add(job_title)
                    else:
                        job_title.company_id = company_info.company_id
                        job_title.name = user_information["job_title"]
                        job_title.lower_case_name = user_information["job_title"].lower()
                        job_title.department_id = department_current.department_id

                    session_db.commit()

                lst_department.append(department_current)

            user_current = session_db.query(UserModel).filter_by(primary_email=user_information["email"]).first()
            if not user_current:
                user_current = UserModel(
                    primary_email=user_information["email"],
                    company_id=company_info.company_id,
                    employee_code=UserModel().generate_employee_code(session_db),
                    name=user_information["name"],
                    phone_number=user_information["mobile"],
                    gender=user_information["gender"],
                    lark_user_id=lark_user_id,
                    open_user_id=user_information["open_id"],
                    thumb_avatar_link=thumb_avatar_link,
                    status=user_status,
                    start_onboard_at=to_time_at(user_information["join_time"]),
                    employment_type_id=user_information["employee_type"],
                    leader_user_id=user_information["leader_user_id"],
                    departments=lst_department,
                    job_title_id=job_title.job_title_id,
                    unsigned_name=unsigned_name,
                    roles=user_information_roles,
                    order=2 if user_information["leader_user_id"] else 1,
                )
                session_db.add(user_current)
            else:
                user_current.status = user_status
                user_current.thumb_avatar_link = thumb_avatar_link
                user_current.employment_type_id = user_information["employee_type"]
                user_current.leader_user_id = user_information.get("leader_user_id")
                user_current.gender = user_information["gender"]
                user_current.phone_number = user_information["mobile"]
                user_current.name = user_information["name"]
                user_current.lark_user_id = lark_user_id
                user_current.start_onboard_at = to_time_at(user_information["join_time"])
                user_current.open_user_id = user_information["open_id"]
                if job_title:
                    user_current.job_title_id = job_title.job_title_id
                user_current.unsigned_name = unsigned_name
                user_current.roles = user_information_roles
                user_current.order = 1
                user_current.departments = lst_department
            # mbo_user_id = user_current.user_id
            # if mbo_user_id not in user_department_data:
            #     user_department_data[mbo_user_id] = []
            # user_department_data[mbo_user_id].append(mbo_department_id)
            session_db.commit()
            # replace leader_user_id of user
        users_in_system = session_db.query(UserModel).all()
        # bod_department = session_db.query(DepartmentModel).filter_by(lower_case_name="bod").first()
        for user in users_in_system:
            open_user_id = user.open_user_id

            if open_user_id in mapping_user_c_level:
                user.leader_user_id = None
            # Update user leader id
            if user.leader_user_id:
                leader = session_db.query(UserModel).filter_by(open_user_id=user.leader_user_id).first()
                user.leader_user_id = leader.user_id if leader else None
                if leader:
                    leader.departments.extend(user.departments)

            session_db.commit()

        # Update owners in department
        list_department_system = session_db.query(DepartmentModel).all()
        for department_system in list_department_system:
            department_in_lark = mapping_department_id_with_data.get(department_system.lark_department_id)
            if not department_in_lark:
                continue
            leaders = department_in_lark.get("leaders", [])
            if not leaders:
                continue
            owners = []
            for leader in leaders:
                lark_leader_user_id = leader.get("leaderID")
                lark_leader_type = leader.get("leaderType")
                user = session_db.query(UserModel).filter_by(lark_user_id=lark_leader_user_id).first()
                open_user = user.open_user_id
                leader_of_leader = user.leader_user_id
                if leader_of_leader:
                    owners.append(leader_of_leader)
                if lark_leader_type == 1 or open_user in mapping_user_c_level:
                    owners.append(user.user_id)
            department_system.owners = owners
        session_db.commit()
        # MobioLogging().info(f"sync_data_from_lark::user_department_data::{user_department_data}")

    def owner_do(self):
        MobioLogging().info(f"owner_do::start")
        lock_data = HandlerSyncDataFromLark.Locker.get_lock_data()
        if lock_data.get("is_locked", False):
            MobioLogging().info(f"HandlerSyncDataFromLark::owner_do::is locked before::{lock_data}")
            return
        try:
            HandlerSyncDataFromLark.Locker.lock()
            HandlerSyncDataFromLark.sync_data_from_lark()
        except Exception as e:
            import traceback

            MobioLogging().error(f"owner_do::error::{str(e)}::{traceback.format_exc()}")
        finally:
            HandlerSyncDataFromLark.Locker.unlock()
        MobioLogging().info(f"owner_do::done")
        return


if __name__ == "__main__":
    HandlerSyncDataFromLark.sync_data_from_lark()
