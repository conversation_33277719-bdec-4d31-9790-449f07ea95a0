#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

from typing import Optional

from src.repositories.log_user_repository import LogUserRepository
from src.services import BaseService


class LogUserService(BaseService):
    def __init__(self):
        super().__init__()
        self.log_user_repository = LogUserRepository()

    async def log_admin_access(
        self,
        username: str,
        primary_email: str,
        path: str,
        method: str,
        user_roles: list,
        user_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        additional_data: Optional[dict] = None,
    ) -> str:
        """
        Log user access via repository
        This method is called from middleware to maintain proper architecture flow

        Args:
            username: Username of the user
            primary_email: Primary email of the user
            path: API path accessed
            method: HTTP method
            user_roles: List of user roles
            user_id: User ID
            ip_address: Client IP address
            user_agent: Client user agent
            additional_data: Additional data to log

        Returns:
            str: Inserted document ID
        """
        return await self.log_user_repository.log_admin_access(
            username=username,
            primary_email=primary_email,
            path=path,
            method=method,
            user_roles=user_roles,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent,
            additional_data=additional_data,
        )
