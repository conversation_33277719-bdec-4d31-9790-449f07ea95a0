#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

from src.repositories.setting_repository import SettingRepository
from src.services import BaseService


class AuthService(BaseService):
    def __init__(self):
        super().__init__()
        self.setting_repository = SettingRepository()

    async def get_information_login(self):
        setting = await self.setting_repository.get_setting()
        authorization_url = setting.get("authorization_url")
        redirect_uri = setting.get("redirect_uri")
        app_id = setting.get("app_id")

        return {"url_redirect": f"{authorization_url}?app_id={app_id}&redirect_uri={redirect_uri}"}
