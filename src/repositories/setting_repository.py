#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

from src.models.mongo.setting_model import SettingModel
from src.repositories.base_repository import BaseRepository


class SettingRepository(BaseRepository):

    def __init__(self):
        super().__init__()
        self.setting_model = SettingModel()

    async def get_setting(self):
        return await self.setting_model.find_one({})
