#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

import datetime
from typing import Any, Dict, Optional

from src.models.mongodb.log_user_model import LogUserModel


class LogUserRepository:
    """
    Repository for handling log user data access operations
    Following the layered architecture pattern: Controller → Repository → Model
    """

    def __init__(self):
        self.log_model = LogUserModel()

    async def log_admin_access(
        self,
        username: str,
        primary_email: str,
        path: str,
        method: str,
        user_roles: list,
        user_id: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Log admin user access for monitoring purposes

        Args:
            username: Username of the user
            primary_email: Primary email of the user
            path: API path accessed
            method: HTTP method (GET, POST, etc.)
            user_roles: List of user roles
            user_id: User ID
            ip_address: Client IP address
            user_agent: Client user agent
            additional_data: Additional data to log

        Returns:
            str: Inserted document ID
        """

        log_data = {
            "username": username,
            "primary_email": primary_email,
            "user_id": user_id,
            "path": path,
            "method": method,
            "roles": user_roles,
            "is_admin": "admin" in [role.lower() for role in user_roles],
            "time_request": datetime.datetime.now(datetime.timezone.utc),
            "ip_address": ip_address,
            "user_agent": user_agent,
            "log_type": "admin_access" if "admin" in [role.lower() for role in user_roles] else "user_access",
        }

        if additional_data:
            log_data.update(additional_data)
        return await self.log_model.log_admin_access(log_data)

    async def get_admin_access_logs(
        self,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        username: Optional[str] = None,
        path: Optional[str] = None,
        page: int = 1,
        per_page: int = 50,
    ) -> Dict[str, Any]:
        """
        Retrieve admin access logs with filtering

        Args:
            start_date: Start date filter
            end_date: End date filter
            username: Username filter
            path: Path filter
            page: Page number
            per_page: Items per page

        Returns:
            Dict containing logs and pagination info
        """
        return await self.log_model.get_admin_access_logs(
            start_date=start_date, end_date=end_date, username=username, path=path, page=page, per_page=per_page
        )

    async def get_user_activity_stats(
        self,
        username: Optional[str] = None,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
    ) -> Dict[str, Any]:
        """
        Get user activity statistics

        Args:
            username: Username filter
            start_date: Start date filter
            end_date: End date filter

        Returns:
            Dict containing activity statistics
        """
        return await self.log_model.get_user_activity_stats(username=username, start_date=start_date, end_date=end_date)

    async def get_user_logs_by_username(
        self,
        username: str,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        page: int = 1,
        per_page: int = 100,
    ) -> Dict[str, Any]:
        """
        Get logs for a specific user

        Args:
            username: Username to filter by
            start_date: Start date filter
            end_date: End date filter
            page: Page number
            per_page: Items per page

        Returns:
            Dict containing user logs and pagination info
        """
        return await self.log_model.get_admin_access_logs(
            start_date=start_date, end_date=end_date, username=username, page=page, per_page=per_page
        )
