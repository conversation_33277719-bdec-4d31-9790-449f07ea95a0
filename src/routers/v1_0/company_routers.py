from fastapi import APIRouter, Depends, Request
from sqlalchemy.orm import Session

from configs.database import get_db
from src.auth.authentication import check_authentication
from src.controllers.company_controller import CompanyController
from src.middleware.handle_response import CustomHandleResponseRoute
from src.routers.uri import URI
from src.schemas.pydantic import BaseResponseSchema

routers = APIRouter(route_class=CustomHandleResponseRoute)


@routers.get(path=URI.COMPANY.COMPANY, dependencies=[Depends(check_authentication)], response_model=BaseResponseSchema)
async def get_company_info(request: Request, session: Session = Depends(get_db)):
    return await CompanyController(request, session).get_company_information()


@routers.post(
    path=URI.COMPANY.SYNC_LARK_DATA,
    dependencies=[],
)
async def sync_lark_data(request: Request):
    return await CompanyController(request).sync_lark_data()
