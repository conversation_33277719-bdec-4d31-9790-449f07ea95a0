#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 17/08/2025
"""

from datetime import datetime
from typing import List, Optional, Dict, Any

from pydantic import BaseModel, Field

from src.schemas.pydantic import BaseResponseSchema


class AdminAccessLogSchema(BaseModel):
    """Schema for individual admin access log entry"""
    username: str = Field(..., description="Username of the user")
    user_id: str = Field(..., description="User ID")
    path: str = Field(..., description="API path accessed")
    method: str = Field(..., description="HTTP method")
    roles: List[str] = Field(..., description="User roles")
    is_admin: bool = Field(..., description="Whether user has admin role")
    datetime: datetime = Field(..., description="Access datetime")
    timestamp: float = Field(..., description="Unix timestamp")
    ip_address: Optional[str] = Field(None, description="Client IP address")
    user_agent: Optional[str] = Field(None, description="Client user agent")
    log_type: str = Field(..., description="Type of log entry")
    query_params: Optional[Dict[str, Any]] = Field(None, description="Query parameters")
    request_id: Optional[str] = Field(None, description="Request ID if available")


class AdminAccessLogsDataSchema(BaseModel):
    """Schema for admin access logs response data"""
    logs: List[AdminAccessLogSchema] = Field(..., description="List of access logs")
    total_count: int = Field(..., description="Total number of logs")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")
    total_pages: int = Field(..., description="Total number of pages")


class AdminAccessLogsResponseSchema(BaseResponseSchema):
    """Response schema for admin access logs endpoint"""
    data: AdminAccessLogsDataSchema


class UserActivityStatSchema(BaseModel):
    """Schema for individual user activity statistics"""
    username: str = Field(..., description="Username")
    is_admin: bool = Field(..., description="Whether user has admin role")
    total_requests: int = Field(..., description="Total number of requests")
    unique_paths_count: int = Field(..., description="Number of unique paths accessed")
    unique_paths: List[str] = Field(..., description="List of unique paths")
    last_access: datetime = Field(..., description="Last access time")
    first_access: datetime = Field(..., description="First access time")


class UserActivityStatsDataSchema(BaseModel):
    """Schema for user activity statistics response data"""
    user_statistics: List[UserActivityStatSchema] = Field(..., description="List of user statistics")


class UserActivityStatsResponseSchema(BaseResponseSchema):
    """Response schema for user activity statistics endpoint"""
    data: UserActivityStatsDataSchema


class AdminAccessLogFiltersSchema(BaseModel):
    """Schema for admin access log filtering parameters"""
    start_date: Optional[str] = Field(None, description="Start date filter (ISO format or YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date filter (ISO format or YYYY-MM-DD)")
    username: Optional[str] = Field(None, description="Username filter (supports partial match)")
    path: Optional[str] = Field(None, description="Path filter (supports partial match)")
    page: int = Field(1, ge=1, description="Page number")
    per_page: int = Field(50, ge=1, le=200, description="Items per page")


class UserActivityStatsFiltersSchema(BaseModel):
    """Schema for user activity statistics filtering parameters"""
    username: Optional[str] = Field(None, description="Username filter")
    start_date: Optional[str] = Field(None, description="Start date filter (ISO format or YYYY-MM-DD)")
    end_date: Optional[str] = Field(None, description="End date filter (ISO format or YYYY-MM-DD)")


class CurrentUserActivityFiltersSchema(BaseModel):
    """Schema for current user activity filtering parameters"""
    days_back: int = Field(7, ge=1, le=90, description="Number of days to look back")