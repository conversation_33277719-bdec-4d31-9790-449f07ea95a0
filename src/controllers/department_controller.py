#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 31/08/2024
"""
from fastapi import Request
from sqlalchemy.orm import Session

from src.common.choices import QueryParamChoice, RoleChoice
from src.common.common import CommonKey
from src.common.message import SuccessMessage
from src.controllers.base_controller import BaseController
from src.repositories.department_repository import DepartmentRepository
from src.repositories.users_repository import UserRepository
from src.schemas.pydantic.department_schemas import (
    ChangeOrderDepartmentsRequestSchema,
    GetDepartmentsSchema,
)


class DepartmentController(BaseController):
    async def get_department_ids_for_abac(self, roles, user):

        # [!] Nếu là admin thì trả về tất cả các phòng ban
        department_ids = []
        if RoleChoice.ADMIN.value in roles:
            return []

        if RoleChoice.LEADER.value in roles or RoleChoice.USER.value in roles:
            department_ids.extend([department.department_id for department in user.departments])

        return department_ids

    async def get_departments(self, request: Request, session: Session):

        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        search = request.query_params.get(QueryParamChoice.SEARCH, "")
        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))
        is_show_department_hidden = int(
            request.query_params.get(QueryParamChoice.DISPLAY, 0)
        )  # [!] có show các phòng ban bị ẩn hay không?

        display = None
        if is_show_department_hidden == 1:
            display = 0
        elif is_show_department_hidden == 0:
            display = 1

        request_department_ids = request.query_params.get(QueryParamChoice.DEPARTMENT_IDS)
        if request_department_ids:
            request_department_ids = request_department_ids.split(",")

        department_repo: DepartmentRepository = DepartmentRepository(session)
        user_repo: UserRepository = UserRepository(session)

        roles, _ = await self.get_roles_and_permissions_from_token(request)
        user = await user_repo.get_user_by_id(account_id)

        department_ids = await self.get_department_ids_for_abac(roles, user)

        list_department_id_query = []
        if request_department_ids:
            if not department_ids:
                list_department_id_query = request_department_ids
            else:
                for request_department_id in request_department_ids:
                    if request_department_id in department_ids:
                        list_department_id_query.append(request_department_id)
        else:
            list_department_id_query = department_ids

        departments, paging = await department_repo.get_departments(
            search, list_department_id_query, company_id, sort, order, page, per_page, display
        )

        results = []
        for department in departments:
            get_department_schema = GetDepartmentsSchema(
                department_id=department.department_id,
                name=department.name,
                display=department.display,
                level=1,
                order=department.order,
            )
            results.append(get_department_schema.dict(exclude_none=True))
        self.logger.info("{} :: departments :: {}".format(self.get_departments.__name__, results))
        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
                CommonKey.DATA: results,
                CommonKey.PAGING: paging,
            }
        )

    async def get_departments_for_org_chart(self, request: Request, session: Session):
        company_id = await self.get_company_id_from_token(request)

        search = request.query_params.get(QueryParamChoice.SEARCH, "")
        sort = request.query_params.get(QueryParamChoice.SORT, CommonKey.UPDATED_TIME)
        order = int(request.query_params.get(QueryParamChoice.ORDER, -1))
        page = int(request.query_params.get(QueryParamChoice.PAGE, 1))
        per_page = int(request.query_params.get(QueryParamChoice.PER_PAGE, 20))

        department_ids = request.query_params.get(QueryParamChoice.DEPARTMENT_IDS)
        if department_ids:
            department_ids = department_ids.split(",")

        department_repo: DepartmentRepository = DepartmentRepository(session)
        display = 1
        departments, paging = await department_repo.get_departments(
            search, department_ids, company_id, sort, order, page, per_page, display
        )

        results = []
        for department in departments:
            get_department_schema = GetDepartmentsSchema(
                department_id=department.department_id,
                name=department.name,
                display=department.display,
                level=1,
                order=department.order,
            )
            results.append(get_department_schema.dict(exclude_none=True))
        self.logger.info("{} :: departments :: {}".format(self.get_departments.__name__, results))
        return await self.json_encoder(
            {
                CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS,
                CommonKey.DATA: results,
                CommonKey.PAGING: paging,
            }
        )

    async def update_order_for_departments(
        self, request: Request, payload: ChangeOrderDepartmentsRequestSchema, session: Session
    ):

        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        department_ids = payload.departments
        department_ids = department_ids.split(",")

        department_repo: DepartmentRepository = DepartmentRepository(session)

        for department_order in payload.departments:
            await department_repo.update_order_for_department(
                company_id, department_order.department_id, department_order.order
            )

        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.UPDATE_SUCCESS})

    async def get_department_owners(self, request: Request, session: Session):
        company_id = await self.get_company_id_from_token(request)
        account_id = await self.get_user_id_from_token(request)

        department_repo: DepartmentRepository = DepartmentRepository(session)
        department_ids = request.query_params.get(QueryParamChoice.DEPARTMENT_IDS, "")
        department_ids = department_ids.split(",") if department_ids else []
        department_owners = await department_repo.get_department_owners(company_id, department_ids)

        results = []
        for department_owner in department_owners:
            get_department_schema = {
                "department_id": department_owner.department_id,
                "owner_ids": department_owner.owners,
            }
            results.append(get_department_schema)
        return await self.json_encoder({CommonKey.MESSAGE: SuccessMessage.GET_SUCCESS, CommonKey.DATA: results})
