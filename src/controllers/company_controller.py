import datetime
import os
import subprocess

from configs import ApplicationConfig
from src.common.caching import lru_cache_m_caching
from src.common.common import CommonKey, CompanyKey, UserKey
from src.common.custom_exception import AccessDenied
from src.common.message import ErrorsMessage
from src.controllers.base_controller import BaseController
from src.libs.lark_docs_sdk.oapi.object.company import LarkObjectCompanyApi
from src.repositories.department_repository import DepartmentRepository
from src.repositories.users_repository import UserRepository


class CompanyController(BaseController):

    # @lru_cache_m_caching.add_for_class_async(expiration=3600)
    async def get_company_information(self):
        company_api = LarkObjectCompanyApi()
        user_repo: UserRepository = UserRepository(self.session)
        department_repo: DepartmentRepository = DepartmentRepository(self.session)

        company_info_future = company_api.get_company_information()
        print('==================check ====================\n')
        print(company_info_future)
        print('==================check ====================\n')
        users_count_future = await user_repo.count_users()
        departments_count_future = await department_repo.count_departments()

        company_info_future = company_info_future.get("tenant", {})

        result = {
            CommonKey.DATA: {
                CompanyKey.AVATAR: company_info_future.get("avatar", {}).get("avatar_origin"),
                CompanyKey.NAME: company_info_future.get("name"),
                CompanyKey.USERS_COUNT: users_count_future,
                CompanyKey.DEPARTMENTS_COUNT: departments_count_future,
            }
        }
        return result

    async def sync_lark_data(self):
        from src.cronjobs.handler_migrate_sync_data_from_lark_to_ladder import (
            HandlerSyncDataFromLark,
        )

        current_user_data = await self.get_user_data_from_token()
        # Check pem
        if "admin" not in current_user_data.get(UserKey.ROLES, []):
            raise AccessDenied(self.get_lang(self.lang).get(ErrorsMessage.NOT_PERMISSION).get(CommonKey.MESSAGE), 403)

        # Check locking
        lock_data = HandlerSyncDataFromLark.Locker.get_lock_data()
        if lock_data.get("is_locked", False):
            return await self.json_encoder(
                {
                    CommonKey.CODE: 400,
                    CommonKey.MESSAGE: "Không thể đồng bộ do có 1 luồng khác đang xử lý",
                    CommonKey.DATA: lock_data,
                }
            )

        # Add to bg task
        time_now = datetime.datetime.now(datetime.UTC)
        log_file = os.path.join(
            ApplicationConfig.LADDER_HOME
            + "/monitor_logs/lark_sync_{}.log".format(time_now.strftime("%Y_%m_%d_%H_%M_%S"))
        )
        with open(log_file, "w") as f:
            process = subprocess.Popen(
                ["python3.11", "-m", "src.cronjobs.handler_migrate_sync_data_from_lark_to_ladder"], stdout=f, stderr=f
            )

        return await self.json_encoder({CommonKey.MESSAGE: "Sync lark data processing in background!!"})
