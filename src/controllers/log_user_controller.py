#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 17/08/2025
"""

from fastapi import Request, Response
from mobio.libs.logging import MobioLogging

from src.auth.authentication import Authentication
from src.common.choices import RoleChoice
from src.controllers.base_controller import BaseController
from src.services.log_user_service import LogUserService


class LogUserController(BaseController):
    def __init__(self):
        super().__init__()
        self.log_user_service = LogUserService()

    async def log_admin_access(self, request: Request, response: Response):
        """Log admin access in background"""
        try:
            # Get user and roles from token
            user, roles = await Authentication().get_user_and_roles(request)

            # Check if user has admin role
            has_admin_role = RoleChoice.ADMIN.value in roles

            # Log access for admin users or if monitoring is enabled for all users
            if has_admin_role or True:  # Set to True to log all users, False for admin only
                # Extract client information
                client_ip = self._get_client_ip(request)
                user_agent = request.headers.get("User-Agent", "Unknown")

                # Get username (assuming it's the primary_email or you have a username field)
                primary_email = getattr(user, "primary_email", "Unknown")
                name = getattr(user, "name", "Unknown")
                user_id = getattr(user, "user_id", getattr(user, "id", "Unknown"))

                # Log the access via controller (following proper architecture flow)
                await self.log_user_service.log_admin_access(
                    username=name,
                    primary_email=primary_email,
                    path=request.url.path,
                    method=request.method,
                    user_roles=roles,
                    user_id=str(user_id),
                    ip_address=client_ip,
                    user_agent=user_agent,
                    additional_data={
                        "query_params": dict(request.query_params),
                    },
                )

        except Exception as e:
            # Don't let logging errors affect the main request
            # You might want to log this error to a different system
            MobioLogging().error(f"Error logging admin access: {str(e)}")

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request headers"""
        # Check for forwarded headers first (for load balancers/proxies)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            # X-Forwarded-For can contain multiple IPs, take the first one
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # Fall back to direct client IP
        if hasattr(request, "client") and request.client:
            return request.client.host

        return "Unknown"
