-- public.company definition

-- Drop table

-- DROP TABLE public.company;

CREATE TABLE public.company (
	company_id varchar NOT NULL,
	tenant_key varchar NULL,
	"name" varchar NULL,
	avatar varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT company_pkey PRIMARY KEY (company_id)
);
CREATE INDEX ix_company_company_id ON public.company USING btree (company_id);


-- public.branch definition

-- Drop table

-- DROP TABLE public.branch;

CREATE TABLE public.branch (
	branch_id varchar NOT NULL,
	company_id varchar NULL,
	code varchar NULL,
	"name" varchar NOT NULL,
	address varchar NOT NULL,
	phone varchar NOT NULL,
	owner_code varchar NOT NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT branch_pkey PRIMARY KEY (branch_id),
	CONSTRAINT branch_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id)
);
CREATE INDEX ix_branch_branch_id ON public.branch USING btree (branch_id);
CREATE INDEX ix_branch_code ON public.branch USING btree (code);


-- public.contract definition

-- Drop table

-- DROP TABLE public.contract;

CREATE TABLE public.contract (
	contract_id serial4 NOT NULL,
	company_id varchar NULL,
	contract_number varchar NULL,
	contract_file_scan_link varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT contract_pkey PRIMARY KEY (contract_id),
	CONSTRAINT contract_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id)
);
CREATE UNIQUE INDEX ix_contract_contract_number ON public.contract USING btree (contract_number);


-- public.department definition

-- Drop table

-- DROP TABLE public.department;

CREATE TABLE public.department (
	department_id varchar NOT NULL,
	company_id varchar NULL,
	department_code varchar NULL,
	lark_department_id varchar NULL,
	open_department_id varchar NULL,
	display int4 NULL,
	"name" varchar NULL,
	lower_case_name varchar NULL,
	description varchar NULL,
	"order" int4 NULL,
	owners _varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT department_lower_case_name_key UNIQUE (lower_case_name),
	CONSTRAINT department_name_key UNIQUE (name),
	CONSTRAINT department_pkey PRIMARY KEY (department_id),
	CONSTRAINT department_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id)
);
CREATE INDEX ix_department_department_id ON public.department USING btree (department_id);


-- public.employment_type definition

-- Drop table

-- DROP TABLE public.employment_type;

CREATE TABLE public.employment_type (
	employment_type_id serial4 NOT NULL,
	company_id varchar NULL,
	vi_name varchar NULL,
	en_name varchar NULL,
	description varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT employment_type_employment_type_id_key UNIQUE (employment_type_id),
	CONSTRAINT employment_type_pkey PRIMARY KEY (employment_type_id),
	CONSTRAINT employment_type_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id)
);


-- public.job_title definition

-- Drop table

-- DROP TABLE public.job_title;

CREATE TABLE public.job_title (
	job_title_id varchar NOT NULL,
	department_id varchar NULL,
	company_id varchar NULL,
	"name" varchar NOT NULL,
	lower_case_name varchar NOT NULL,
	description varchar NULL,
	"order" int4 NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT job_title_pkey PRIMARY KEY (job_title_id),
	CONSTRAINT job_title_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id),
	CONSTRAINT job_title_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.department(department_id)
);
CREATE INDEX ix_job_title_department_id ON public.job_title USING btree (department_id);
CREATE INDEX ix_job_title_job_title_id ON public.job_title USING btree (job_title_id);


-- public.job_title_level definition

-- Drop table

-- DROP TABLE public.job_title_level;

CREATE TABLE public.job_title_level (
	job_title_level_id varchar NOT NULL,
	job_title_id varchar NULL,
	company_id varchar NULL,
	"name" varchar NOT NULL,
	"level" int4 NOT NULL,
	lower_case_name varchar NOT NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT job_title_level_pkey PRIMARY KEY (job_title_level_id),
	CONSTRAINT job_title_level_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id),
	CONSTRAINT job_title_level_job_title_id_fkey FOREIGN KEY (job_title_id) REFERENCES public.job_title(job_title_id)
);
CREATE INDEX ix_job_title_level_job_title_id ON public.job_title_level USING btree (job_title_id);
CREATE INDEX ix_job_title_level_job_title_level_id ON public.job_title_level USING btree (job_title_level_id);


-- public."permission" definition

-- Drop table

-- DROP TABLE public."permission";

CREATE TABLE public."permission" (
	permission_id varchar NOT NULL,
	company_id varchar NULL,
	description varchar NULL,
	"action" varchar NOT NULL,
	"scope" varchar NOT NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT permission_pkey PRIMARY KEY (permission_id),
	CONSTRAINT permission_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id)
);
CREATE INDEX ix_permission_permission_id ON public.permission USING btree (permission_id);


-- public.policies definition

-- Drop table

-- DROP TABLE public.policies;

CREATE TABLE public.policies (
	policy_id varchar NOT NULL,
	"name" varchar NOT NULL,
	description varchar NULL,
	policy_type varchar NULL,
	company_id varchar NULL,
	"document" json NOT NULL,
	created_by varchar NOT NULL,
	status int4 NOT NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT policies_name_key UNIQUE (name),
	CONSTRAINT policies_pkey PRIMARY KEY (policy_id),
	CONSTRAINT policies_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id)
);
CREATE INDEX ix_policies_policy_id ON public.policies USING btree (policy_id);


-- public."role" definition

-- Drop table

-- DROP TABLE public."role";

CREATE TABLE public."role" (
	role_id varchar NOT NULL,
	"name" varchar NOT NULL,
	lower_case_name varchar NOT NULL,
	description varchar NULL,
	company_id varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT role_lower_case_name_key UNIQUE (lower_case_name),
	CONSTRAINT role_name_key UNIQUE (name),
	CONSTRAINT role_pkey PRIMARY KEY (role_id),
	CONSTRAINT role_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id)
);
CREATE INDEX ix_role_role_id ON public.role USING btree (role_id);


-- public.role_permission definition

-- Drop table

-- DROP TABLE public.role_permission;

CREATE TABLE public.role_permission (
	role_permission_id varchar NOT NULL,
	role_id varchar NULL,
	permission_id varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT role_permission_pkey PRIMARY KEY (role_permission_id),
	CONSTRAINT role_permission_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public."permission"(permission_id),
	CONSTRAINT role_permission_role_id_fkey FOREIGN KEY (role_id) REFERENCES public."role"(role_id)
);
CREATE INDEX ix_role_permission_permission_id ON public.role_permission USING btree (permission_id);
CREATE INDEX ix_role_permission_role_id ON public.role_permission USING btree (role_id);
CREATE INDEX ix_role_permission_role_permission_id ON public.role_permission USING btree (role_permission_id);


-- public.role_policies definition

-- Drop table

-- DROP TABLE public.role_policies;

CREATE TABLE public.role_policies (
	role_policy_id varchar NOT NULL,
	role_id varchar NULL,
	policy_id varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT role_policies_pkey PRIMARY KEY (role_policy_id),
	CONSTRAINT role_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id),
	CONSTRAINT role_policies_role_id_fkey FOREIGN KEY (role_id) REFERENCES public."role"(role_id)
);
CREATE INDEX ix_role_policies_policy_id ON public.role_policies USING btree (policy_id);
CREATE INDEX ix_role_policies_role_id ON public.role_policies USING btree (role_id);
CREATE INDEX ix_role_policies_role_policy_id ON public.role_policies USING btree (role_policy_id);


-- public.sub_department definition

-- Drop table

-- DROP TABLE public.sub_department;

CREATE TABLE public.sub_department (
	sub_department_id varchar NOT NULL,
	company_id varchar NULL,
	code varchar NOT NULL,
	"name" varchar NULL,
	department_id varchar NULL,
	description varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT sub_department_code_key UNIQUE (code),
	CONSTRAINT sub_department_pkey PRIMARY KEY (sub_department_id, code),
	CONSTRAINT sub_department_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id),
	CONSTRAINT sub_department_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.department(department_id)
);
CREATE UNIQUE INDEX ix_sub_department_sub_department_id ON public.sub_department USING btree (sub_department_id);


-- public."user" definition

-- Drop table

-- DROP TABLE public."user";

CREATE TABLE public."user" (
	user_id varchar NOT NULL,
	employee_code varchar NOT NULL,
	company_id varchar NULL,
	first_name varchar NULL,
	lark_user_id varchar NOT NULL,
	open_user_id varchar NOT NULL,
	middle_name varchar NULL,
	last_name varchar NULL,
	"name" varchar NOT NULL,
	unsigned_name varchar NULL,
	primary_email varchar NOT NULL,
	personal_email varchar NULL,
	username varchar NULL,
	"password" varchar NULL,
	gender int4 NOT NULL,
	marital_status int4 NULL,
	leader_user_id varchar NULL,
	education_level varchar NULL,
	employment_type_id int4 NULL,
	thumb_avatar_link varchar NULL,
	icon_avatar_link varchar NULL,
	home_town varchar NULL,
	date_of_birth timestamp NULL,
	current_address varchar NULL,
	phone_number varchar NULL,
	last_time_login timestamp NULL,
	salary_amount float8 NULL,
	start_salary timestamp NULL,
	start_onboard_at timestamp NULL,
	"order" int4 NULL,
	contract_number varchar NULL,
	job_title_id varchar NOT NULL,
	job_title_level_id varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT user_contract_number_key UNIQUE (contract_number),
	CONSTRAINT user_pkey PRIMARY KEY (user_id, employee_code),
	CONSTRAINT user_primary_email_key UNIQUE (primary_email),
	CONSTRAINT user_username_key UNIQUE (username),
	CONSTRAINT user_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id),
	CONSTRAINT user_contract_number_fkey FOREIGN KEY (contract_number) REFERENCES public.contract(contract_number),
	CONSTRAINT user_employment_type_id_fkey FOREIGN KEY (employment_type_id) REFERENCES public.employment_type(employment_type_id),
	CONSTRAINT user_job_title_id_fkey FOREIGN KEY (job_title_id) REFERENCES public.job_title(job_title_id),
	CONSTRAINT user_job_title_level_id_fkey FOREIGN KEY (job_title_level_id) REFERENCES public.job_title_level(job_title_level_id)
);
CREATE UNIQUE INDEX ix_user_employee_code ON public."user" USING btree (employee_code);
CREATE INDEX ix_user_job_title_id ON public."user" USING btree (job_title_id);
CREATE INDEX ix_user_job_title_level_id ON public."user" USING btree (job_title_level_id);
CREATE UNIQUE INDEX ix_user_user_id ON public."user" USING btree (user_id);


-- public.user_department definition

-- Drop table

-- DROP TABLE public.user_department;

CREATE TABLE public.user_department (
	user_department_id varchar NOT NULL,
	user_id varchar NULL,
	department_id varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT user_department_pkey PRIMARY KEY (user_department_id),
	CONSTRAINT user_department_department_id_fkey FOREIGN KEY (department_id) REFERENCES public.department(department_id),
	CONSTRAINT user_department_user_id_fkey FOREIGN KEY (user_id) REFERENCES public."user"(user_id)
);
CREATE INDEX ix_user_department_department_id ON public.user_department USING btree (department_id);
CREATE INDEX ix_user_department_user_department_id ON public.user_department USING btree (user_department_id);
CREATE INDEX ix_user_department_user_id ON public.user_department USING btree (user_id);


-- public.user_event definition

-- Drop table

-- DROP TABLE public.user_event;

CREATE TABLE public.user_event (
	user_event_id serial4 NOT NULL,
	employee_code varchar NULL,
	body_event json NULL,
	event_type varchar NOT NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT user_event_pkey PRIMARY KEY (user_event_id),
	CONSTRAINT user_event_employee_code_fkey FOREIGN KEY (employee_code) REFERENCES public."user"(employee_code)
);


-- public.user_history definition

-- Drop table

-- DROP TABLE public.user_history;

CREATE TABLE public.user_history (
	history_id serial4 NOT NULL,
	employee_code varchar NULL,
	before_data json NULL,
	after_data json NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT user_history_pkey PRIMARY KEY (history_id),
	CONSTRAINT user_history_employee_code_fkey FOREIGN KEY (employee_code) REFERENCES public."user"(employee_code)
);


-- public.user_identification definition

-- Drop table

-- DROP TABLE public.user_identification;

CREATE TABLE public.user_identification (
	user_identification_id serial4 NOT NULL,
	company_id varchar NULL,
	employee_code varchar NULL,
	"type" varchar NOT NULL,
	issue_place varchar NOT NULL,
	front_image varchar NOT NULL,
	back_image varchar NOT NULL,
	identification_number varchar NULL,
	issue_date timestamp NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT user_identification_pkey PRIMARY KEY (user_identification_id),
	CONSTRAINT user_identification_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id),
	CONSTRAINT user_identification_employee_code_fkey FOREIGN KEY (employee_code) REFERENCES public."user"(employee_code)
);
CREATE INDEX ix_user_identification_employee_code ON public.user_identification USING btree (employee_code);


-- public.user_policies definition

-- Drop table

-- DROP TABLE public.user_policies;

CREATE TABLE public.user_policies (
	user_policy_id varchar NOT NULL,
	user_id varchar NULL,
	policy_id varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT user_policies_pkey PRIMARY KEY (user_policy_id),
	CONSTRAINT user_policies_policy_id_fkey FOREIGN KEY (policy_id) REFERENCES public.policies(policy_id),
	CONSTRAINT user_policies_user_id_fkey FOREIGN KEY (user_id) REFERENCES public."user"(user_id)
);
CREATE INDEX ix_user_policies_policy_id ON public.user_policies USING btree (policy_id);
CREATE INDEX ix_user_policies_user_id ON public.user_policies USING btree (user_id);
CREATE INDEX ix_user_policies_user_policy_id ON public.user_policies USING btree (user_policy_id);


-- public.user_role definition

-- Drop table

-- DROP TABLE public.user_role;

CREATE TABLE public.user_role (
	user_role_id varchar NOT NULL,
	user_id varchar NULL,
	role_id varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT user_role_pkey PRIMARY KEY (user_role_id),
	CONSTRAINT user_role_role_id_fkey FOREIGN KEY (role_id) REFERENCES public."role"(role_id),
	CONSTRAINT user_role_user_id_fkey FOREIGN KEY (user_id) REFERENCES public."user"(user_id)
);
CREATE INDEX ix_user_role_role_id ON public.user_role USING btree (role_id);
CREATE INDEX ix_user_role_user_id ON public.user_role USING btree (user_id);
CREATE INDEX ix_user_role_user_role_id ON public.user_role USING btree (user_role_id);


-- public.user_sub_department definition

-- Drop table

-- DROP TABLE public.user_sub_department;

CREATE TABLE public.user_sub_department (
	user_sub_department_id varchar NOT NULL,
	user_id varchar NULL,
	sub_department_id varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT user_sub_department_pkey PRIMARY KEY (user_sub_department_id),
	CONSTRAINT user_sub_department_sub_department_id_fkey FOREIGN KEY (sub_department_id) REFERENCES public.sub_department(sub_department_id),
	CONSTRAINT user_sub_department_user_id_fkey FOREIGN KEY (user_id) REFERENCES public."user"(user_id)
);
CREATE INDEX ix_user_sub_department_sub_department_id ON public.user_sub_department USING btree (sub_department_id);
CREATE INDEX ix_user_sub_department_user_id ON public.user_sub_department USING btree (user_id);
CREATE INDEX ix_user_sub_department_user_sub_department_id ON public.user_sub_department USING btree (user_sub_department_id);


-- public.contact_info definition

-- Drop table

-- DROP TABLE public.contact_info;

CREATE TABLE public.contact_info (
	contact_id serial4 NOT NULL,
	company_id varchar NULL,
	employee_code varchar NULL,
	full_name varchar NOT NULL,
	email varchar NULL,
	phone varchar NULL,
	address varchar NULL,
	relationship_type varchar NULL,
	status int4 NOT NULL,
	created_by varchar NULL,
	updated_by varchar NULL,
	created_time timestamp NULL,
	updated_time timestamp NULL,
	CONSTRAINT contact_info_pkey PRIMARY KEY (contact_id),
	CONSTRAINT contact_info_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(company_id),
	CONSTRAINT contact_info_employee_code_fkey FOREIGN KEY (employee_code) REFERENCES public."user"(employee_code)
);
CREATE INDEX ix_contact_info_employee_code ON public.contact_info USING btree (employee_code);