# Schema MongoDB Tối Ưu cho Hệ Thống Quản Lý Nhân Sự

## C<PERSON>c Tối Ưu Hóa Hiệu Suất Đã Áp Dụng
- **Phi chuẩn hóa (Denormalization)**: <PERSON><PERSON> liệu tham chiếu quan trọng được nhúng để đọc nhanh hơn
- **<PERSON>húng từng phần (Partial Embedding)**: C<PERSON>c mảng lớn đượ<PERSON> chuyển sang collections riêng biệt
- **Lập chỉ mục thông minh (Smart Indexing)**: Chỉ mục ghép và từng phần cho các truy vấn phổ biến
- **Chiến lược phân mảnh (Sharding Strategy)**: Phân vùng nhận biết đa tenant
- **Mẫu truy vấn (Query Patterns)**: Tối ưu hóa cho các mô hình truy cập phổ biến

## Tổng Quan Các Collections

### 1. companies (Công ty)
```javascript
{
  _id: ObjectId,
  company_id: String, // định danh duy nhất
  tenant_key: String, // khóa tenant
  name: String, // tên công ty
  avatar: String, // ảnh đại diện công ty
  status: Number, // trạng thái (0=không hoạt động, 1=hoạt động)
  created_by: String, // người tạo
  updated_by: String, // người cập nhật
  created_time: Date, // thời gian tạo
  updated_time: Date // thời gian cập nhật
}
```

**Chỉ mục tối ưu:**
- `{ company_id: 1 }` (duy nhất)
- `{ tenant_key: 1 }`

### 2. users (Người dùng - Collection lõi đã tối ưu)
```javascript
{
  _id: ObjectId,
  user_id: String, // định danh duy nhất
  employee_code: String, // mã nhân viên (duy nhất)
  company_id: String, // tham chiếu đến companies.company_id
  
  // Thông tin cá nhân
  first_name: String, // tên
  middle_name: String, // tên đệm
  last_name: String, // họ
  name: String, // họ tên đầy đủ
  unsigned_name: String, // tên không dấu
  primary_email: String, // email chính (duy nhất)
  personal_email: String, // email cá nhân
  username: String, // tên đăng nhập (duy nhất)
  password: String, // mật khẩu
  gender: Number, // giới tính
  marital_status: Number, // tình trạng hôn nhân
  education_level: String, // trình độ học vấn
  date_of_birth: Date, // ngày sinh
  
  // Thông tin liên lạc
  phone_number: String, // số điện thoại
  current_address: String, // địa chỉ hiện tại
  home_town: String, // quê quán
  
  // Tích hợp Lark
  lark_user_id: String, // ID người dùng Lark
  open_user_id: String, // ID người dùng mở
  
  // Thông tin công việc (Phi chuẩn hóa để tăng hiệu suất)
  employment: {
    type_id: Number, // ID loại hình việc làm
    type_name: String, // tên loại hình việc làm (phi chuẩn hóa từ employment_types)
    job_title: { // chức danh công việc
      id: String, // ID chức danh
      name: String, // tên chức danh
      department_id: String, // ID phòng ban
      department_name: String // tên phòng ban (phi chuẩn hóa)
    },
    job_level: { // cấp bậc công việc
      id: String, // ID cấp bậc
      name: String, // tên cấp bậc
      level: Number // số cấp bậc
    }
  },
  salary_amount: Number, // số tiền lương
  start_salary: Date, // ngày bắt đầu lương
  start_onboard_at: Date, // ngày nhận việc
  contract_number: String, // số hợp đồng
  order: Number // thứ tự
  
  // Cấu trúc cấp bậc lãnh đạo (Tối ưu)
  leader: {
    user_id: String, // ID người quản lý
    name: String, // tên người quản lý (phi chuẩn hóa để truy cập nhanh)
    employee_code: String // mã nhân viên quản lý
  },
  
  // Ảnh đại diện
  avatar: {
    thumb: String, // ảnh thumbnail
    icon: String // ảnh icon
  },
  
  // Các trường hệ thống
  last_time_login: Date, // lần đăng nhập cuối
  status: Number, // trạng thái
  created_by: String, // người tạo
  updated_by: String, // người cập nhật
  created_time: Date, // thời gian tạo
  updated_time: Date, // thời gian cập nhật
  
  // Các mối quan hệ tối ưu (Chỉ tham chiếu nhẹ)
  departments: [String], // chỉ department_ids
  sub_departments: [String], // chỉ sub_department_ids
  roles: [String], // chỉ role_ids
  policies: [String], // chỉ policy_ids
  
  // Các trường truy cập nhanh (Phi chuẩn hóa)
  quick_access: {
    primary_department_name: String, // tên phòng ban chính
    primary_role_name: String, // tên vai trò chính
    manager_name: String, // tên quản lý
    is_manager: Boolean, // có phải quản lý không
    direct_reports_count: Number // số lượng nhân viên quản lý trực tiếp
  }
}
```

**Chỉ mục tối ưu:**
- `{ user_id: 1 }` (duy nhất)
- `{ employee_code: 1 }` (duy nhất)
- `{ primary_email: 1 }` (duy nhất)
- `{ username: 1 }` (duy nhất, thưa)
- `{ company_id: 1, status: 1 }` (ghép cho người dùng hoạt động)
- `{ company_id: 1, "employment.job_title.department_id": 1 }` (truy vấn phòng ban)
- `{ company_id: 1, "leader.user_id": 1 }` (truy vấn cấu trúc)
- `{ "employment.job_title.id": 1 }`
- `{ "employment.contract_number": 1 }` (thưa)
- `{ lark_user_id: 1 }` (thưa)
- `{ company_id: 1, last_time_login: -1 }` (hoạt động gần đây)
- `{ company_id: 1, created_time: -1 }` (nhân viên mới)

### 3. departments (Phòng ban)
```javascript
{
  _id: ObjectId,
  department_id: String, // định danh duy nhất
  company_id: String, // tham chiếu đến companies.company_id
  department_code: String, // mã phòng ban
  lark_department_id: String, // ID phòng ban Lark
  open_department_id: String, // ID phòng ban mở
  display: Number, // hiển thị
  name: String, // tên phòng ban (duy nhất)
  lower_case_name: String, // tên phòng ban viết thường (duy nhất)
  description: String, // mô tả
  order: Number, // thứ tự
  owners: [String], // mảng các định danh người dùng
  status: Number, // trạng thái
  created_by: String, // người tạo
  updated_by: String, // người cập nhật
  created_time: Date, // thời gian tạo
  updated_time: Date, // thời gian cập nhật
  
  // Phòng ban con (nhúng)
  sub_departments: [{
    sub_department_id: String, // ID phòng ban con
    code: String, // mã (duy nhất)
    name: String, // tên
    description: String, // mô tả
    status: Number, // trạng thái
    created_by: String, // người tạo
    updated_by: String, // người cập nhật
    created_time: Date, // thời gian tạo
    updated_time: Date // thời gian cập nhật
  }]
}
```

**Chỉ mục tối ưu:**
- `{ department_id: 1 }` (duy nhất)
- `{ company_id: 1, status: 1 }` (ghép cho phòng ban hoạt động)
- `{ company_id: 1, name: 1 }` (ghép duy nhất)
- `{ company_id: 1, lower_case_name: 1 }` (ghép duy nhất)
- `{ "sub_departments.code": 1 }` (duy nhất, thưa)
- `{ company_id: 1, "sub_departments.status": 1 }` (phòng ban con hoạt động)

### 4. job_titles (Chức danh công việc)
```javascript
{
  _id: ObjectId,
  job_title_id: String, // định danh duy nhất
  department_id: String, // tham chiếu đến departments.department_id
  company_id: String, // tham chiếu đến companies.company_id
  name: String, // tên chức danh
  lower_case_name: String, // tên chức danh viết thường
  description: String, // mô tả
  order: Number, // thứ tự
  status: Number, // trạng thái
  created_by: String, // người tạo
  updated_by: String, // người cập nhật
  created_time: Date, // thời gian tạo
  updated_time: Date, // thời gian cập nhật
  
  // Cấp bậc chức danh (nhúng)
  levels: [{
    job_title_level_id: String, // ID cấp bậc chức danh
    name: String, // tên cấp bậc
    level: Number, // số cấp bậc
    lower_case_name: String, // tên cấp bậc viết thường
    status: Number, // trạng thái
    created_by: String, // người tạo
    updated_by: String, // người cập nhật
    created_time: Date, // thời gian tạo
    updated_time: Date // thời gian cập nhật
  }]
}
```

**Chỉ mục tối ưu:**
- `{ job_title_id: 1 }` (duy nhất)
- `{ company_id: 1, department_id: 1, status: 1 }` (ghép cho chức danh hoạt động)
- `{ department_id: 1, status: 1 }`
- `{ "levels.job_title_level_id": 1 }` (duy nhất)
- `{ company_id: 1, "levels.level": 1 }` (truy vấn cấu trúc cấp bậc)

### 5. roles (Vai trò)
```javascript
{
  _id: ObjectId,
  role_id: String, // định danh duy nhất
  name: String, // tên vai trò (duy nhất)
  lower_case_name: String, // tên vai trò viết thường (duy nhất)
  description: String, // mô tả
  company_id: String, // tham chiếu đến companies.company_id
  status: Number, // trạng thái
  created_by: String, // người tạo
  updated_by: String, // người cập nhật
  created_time: Date, // thời gian tạo
  updated_time: Date, // thời gian cập nhật
  
  // Quyền hạn (nhúng)
  permissions: [{
    permission_id: String, // ID quyền hạn
    description: String, // mô tả
    action: String, // hành động
    scope: String, // phạm vi
    status: Number, // trạng thái
    created_by: String, // người tạo
    updated_by: String, // người cập nhật
    created_time: Date, // thời gian tạo
    updated_time: Date // thời gian cập nhật
  }],
  
  // Chính sách (mảng tham chiếu)
  policies: [String] // mảng policy_ids
}
```

**Chỉ mục tối ưu:**
- `{ role_id: 1 }` (duy nhất)
- `{ company_id: 1, name: 1 }` (ghép duy nhất)
- `{ company_id: 1, lower_case_name: 1 }` (ghép duy nhất)
- `{ company_id: 1, status: 1 }` (vai trò hoạt động)
- `{ "permissions.permission_id": 1 }`
- `{ "permissions.action": 1, "permissions.scope": 1 }` (truy vấn quyền hạn)

### 6. policies (Chính sách)
```javascript
{
  _id: ObjectId,
  policy_id: String, // định danh duy nhất
  name: String, // tên chính sách (duy nhất)
  description: String, // mô tả
  policy_type: String, // loại chính sách
  company_id: String, // tham chiếu đến companies.company_id
  document: Object, // tài liệu JSON
  created_by: String, // người tạo
  status: Number, // trạng thái
  updated_by: String, // người cập nhật
  created_time: Date, // thời gian tạo
  updated_time: Date // thời gian cập nhật
}
```

**Chỉ mục tối ưu:**
- `{ policy_id: 1 }` (duy nhất)
- `{ company_id: 1, name: 1 }` (ghép duy nhất)
- `{ company_id: 1, policy_type: 1, status: 1 }` (truy vấn theo loại)
- `{ company_id: 1, status: 1 }`


### 7. employment_types (Loại hình việc làm)
```javascript
{
  _id: ObjectId,
  employment_type_id: Number, // tương đương auto-increment
  company_id: String, // tham chiếu đến companies.company_id
  vi_name: String, // tên tiếng Việt
  en_name: String, // tên tiếng Anh
  description: String, // mô tả
  status: Number, // trạng thái
  created_by: String, // người tạo
  updated_by: String, // người cập nhật
  created_time: Date, // thời gian tạo
  updated_time: Date // thời gian cập nhật
}
```

**Chỉ mục:**
- `{ employment_type_id: 1 }` (duy nhất)
- `{ company_id: 1, status: 1 }`


### Mối quan hệ chính (Cô lập tenant):
- **companies** ← **users** (company_id) - Phân mảnh
- **companies** ← **departments** (company_id)
- **companies** ← **job_titles** (company_id)
- **companies** ← **roles** (company_id)
- **companies** ← **policies** (company_id)
- **companies** ← **contracts** (company_id)
- **companies** ← **employment_types** (company_id)
- **companies** ← **branches** (company_id)
- **companies** ← **user_events** (company_id) - Phân mảnh
- **companies** ← **user_history** (company_id) - Phân mảnh
- **companies** ← **user_contacts** (company_id)
- **companies** ← **user_identifications** (company_id)

### Mối quan hệ phi chuẩn hóa (Tối ưu hiệu suất):
- **users.employment.job_title** chứa tên chức danh và phòng ban phi chuẩn hóa
- **users.leader** chứa thông tin quản lý phi chuẩn hóa
- **users.quick_access** chứa các giá trị tính toán/cache
- **departments.sub_departments** chứa dữ liệu phòng ban con nhúng
- **job_titles.levels** chứa cấu trúc cấp bậc nhúng
- **roles.permissions** chứa chi tiết quyền hạn nhúng

### Chiến lược phân mảnh:
```javascript
// Khóa phân mảnh cho tối ưu đa tenant
sh.shardCollection("hr.users", { "company_id": 1, "_id": 1 })
sh.shardCollection("hr.user_events", { "company_id": 1, "employee_code": 1 })
sh.shardCollection("hr.user_history", { "company_id": 1, "employee_code": 1 })
```

## Mẫu Tối Ưu Đọc:
```javascript
// Hồ sơ nhân viên với dữ liệu phi chuẩn hóa
db.users.findOne(
  { "company_id": "comp123", "employee_code": "EMP001" },
  { 
    "password": 0, // loại trừ dữ liệu nhạy cảm
    "user_history": 0, // loại trừ mảng lớn
    "user_events": 0 
  }
)

// Danh sách nhân viên phòng ban với dữ liệu tối thiểu
db.users.find(
  { 
    "company_id": "comp123", 
    "employment.job_title.department_id": "dept001",
    "status": 1 
  },
  { 
    "name": 1, 
    "employee_code": 1, 
    "employment.job_title.name": 1,
    "quick_access": 1
  }
)

// Truy vấn cấu trúc quản lý
db.users.find(
  { 
    "company_id": "comp123", 
    "leader.user_id": "mgr001" 
  },
  { "name": 1, "employee_code": 1, "employment.job_title.name": 1 }
)
```

## Mẫu Aggregation:
```javascript
// Thống kê phòng ban
db.users.aggregate([
  { $match: { "company_id": "comp123", "status": 1 } },
  { $group: {
    _id: "$employment.job_title.department_id",
    department_name: { $first: "$employment.job_title.department_name" },
    employee_count: { $sum: 1 },
    avg_salary: { $avg: "$employment.salary_amount" }
  }}
])

// Hoạt động gần đây trong công ty
db.users.aggregate([
  { $match: { 
    "company_id": "comp123", 
    "last_time_login": { $gte: new Date(Date.now() - 30*24*60*60*1000) }
  }},
  { $project: {
    "name": 1,
    "last_time_login": 1,
    "employment.job_title.department_name": 1
  }},
  { $sort: { "last_time_login": -1 } }
])
```

## Hướng Dẫn Hiệu Suất Truy Vấn

### Thực hành tốt nhất:
1. **Luôn bao gồm company_id** trong các truy vấn đa tenant để hiệu quả phân mảnh
2. **Sử dụng projection** để giới hạn các trường trả về, đặc biệt loại trừ tài liệu nhúng lớn
3. **Tận dụng dữ liệu phi chuẩn hóa** cho các hoạt động đọc nhiều
4. **Sử dụng chỉ mục ghép** phù hợp với mẫu truy vấn của bạn
5. **Xem xét tùy chọn đọc** cho khối lượng công việc phân tích (đọc từ secondary)
6. **Triển khai caching** cho dữ liệu tham chiếu được truy cập thường xuyên
7. **Sử dụng aggregation pipelines** cho các truy vấn báo cáo phức tạp
8. **Giám sát truy vấn chậm** và tối ưu hóa chỉ mục phù hợp

### Cân nhắc bộ nhớ:
- **Giới hạn kích thước tài liệu**: Giữ dưới 16MB, tài liệu điển hình nên < 1MB
- **Giới hạn kích thước mảng**: Xem xét collections riêng biệt khi mảng vượt quá 100 phần tử
- **Bộ nhớ chỉ mục**: Đảm bảo working set phù hợp với RAM
- **Cân nhắc phân mảnh**: Cân bằng kích thước chunk trên các shard

### Tối ưu ghi:
- **Hoạt động hàng loạt** cho bulk inserts/updates
- **Sử dụng upserts** khi thích hợp để giảm round trips
- **Xem xét write concerns** dựa trên yêu cầu tính nhất quán
- **Triển khai xử lý lỗi phù hợp** cho lỗi khóa trùng lặp