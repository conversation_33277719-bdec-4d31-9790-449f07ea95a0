#!/usr/bin/env python3
"""
Migration Script: PostgreSQL to MongoDB
Migrates HR management system data from PostgreSQL to MongoDB with optimized schema.
Based on sql.sql and mongodb.schema specifications.
"""

import asyncio
import logging
import sys
from datetime import datetime
from typing import Any, Dict

import asyncpg
from motor.motor_asyncio import AsyncIOMotorClient
import re
from configs import ApplicationConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("migration.log"), logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)


class SQLToMongoMigrator:
    """Main migration class handling PostgreSQL to MongoDB data transfer."""

    def __init__(self):
        self.postgres_url = ApplicationConfig.LADDER_POSTGRES_URI
        self.mongo_url = ApplicationConfig.LADDER_MONGO_URI
        self.mongo_db_name = re.search(r"^mongodb://[^@]+@[^/]+/([^?$]+).*$", ApplicationConfig.LADDER_MONGO_URI).group(1)
        self.pg_conn = None
        self.mongo_client = None
        self.mongo_db = None
        self.migration_stats = {}

    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()

    async def connect(self):
        """Establish connections to both databases."""
        try:
            # Connect to PostgreSQL
            self.pg_conn = await asyncpg.connect(self.postgres_url)
            logger.info("Connected to PostgreSQL")

            # Connect to MongoDB
            self.mongo_client = AsyncIOMotorClient(self.mongo_url)
            self.mongo_db = self.mongo_client[self.mongo_db_name]
            logger.info("Connected to MongoDB")

        except Exception as e:
            logger.error(f"Connection failed: {e}")
            raise

    async def disconnect(self):
        """Close database connections."""
        if self.pg_conn:
            await self.pg_conn.close()
            logger.info("PostgreSQL connection closed")

        if self.mongo_client:
            self.mongo_client.close()
            logger.info("MongoDB connection closed")

    async def migrate_companies(self) -> Dict[str, Any]:
        """Migrate companies table."""
        logger.info("Starting companies migration...")

        query = """
        SELECT company_id, tenant_key, name, avatar, status, 
               created_by, updated_by, created_time, updated_time
        FROM company
        ORDER BY created_time
        """

        rows = await self.pg_conn.fetch(query)
        companies = []

        for row in rows:
            company_doc = {
                "company_id": row["company_id"],
                "tenant_key": row["tenant_key"],
                "name": row["name"],
                "avatar": row["avatar"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            companies.append(company_doc)

        if companies and "companies" not in await self.mongo_db.list_collection_names():
            await self.mongo_db.companies.insert_many(companies)
            logger.info(f"Migrated {len(companies)} companies")

        return {"migrated": len(companies), "collection": "companies"}

    async def migrate_employment_types(self) -> Dict[str, Any]:
        """Migrate employment_type table."""
        logger.info("Starting employment_types migration...")

        query = """
        SELECT employment_type_id, company_id, vi_name, en_name, description,
               status, created_by, updated_by, created_time, updated_time
        FROM employment_type
        ORDER BY employment_type_id
        """

        rows = await self.pg_conn.fetch(query)
        employment_types = []

        for row in rows:
            doc = {
                "employment_type_id": row["employment_type_id"],
                "company_id": row["company_id"],
                "vi_name": row["vi_name"],
                "en_name": row["en_name"],
                "description": row["description"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            employment_types.append(doc)

        if employment_types and "employment_types" not in await self.mongo_db.list_collection_names():
            await self.mongo_db.employment_types.insert_many(employment_types)
            logger.info(f"Migrated {len(employment_types)} employment types")

        return {"migrated": len(employment_types), "collection": "employment_types"}

    async def migrate_policies(self) -> Dict[str, Any]:
        """Migrate policies table."""
        logger.info("Starting policies migration...")

        query = """
        SELECT policy_id, name, description, policy_type, company_id, document,
               created_by, status, updated_by, created_time, updated_time
        FROM policies
        ORDER BY created_time
        """

        rows = await self.pg_conn.fetch(query)
        policies = []

        for row in rows:
            doc = {
                "policy_id": row["policy_id"],
                "name": row["name"],
                "description": row["description"],
                "policy_type": row["policy_type"],
                "company_id": row["company_id"],
                "document": row["document"],
                "created_by": row["created_by"],
                "status": row["status"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            policies.append(doc)

        if policies and "policies" not in await self.mongo_db.list_collection_names():
            await self.mongo_db.policies.insert_many(policies)
            logger.info(f"Migrated {len(policies)} policies")

        return {"migrated": len(policies), "collection": "policies"}

    async def migrate_departments(self) -> Dict[str, Any]:
        """Migrate departments with optimized schema and embedded sub_departments."""
        logger.info("Starting departments migration...")

        # Get main departments
        dept_query = """
        SELECT department_id, company_id, department_code, lark_department_id, 
               open_department_id, display, name, lower_case_name, description, 
               "order", owners, status, created_by, updated_by, created_time, updated_time
        FROM department
        ORDER BY created_time
        """

        rows = await self.pg_conn.fetch(dept_query)

        departments = []
        for row in rows:
            owners = [owner for owner in row["owners"]]
            doc = {
                "department_id": row["department_id"],
                "company_id": row["company_id"],
                "department_code": row["department_code"],
                "lark_department_id": row["lark_department_id"],
                "open_department_id": row["open_department_id"],
                "display": row["display"],
                "name": row["name"],
                "lower_case_name": row["lower_case_name"],
                "description": row["description"],
                "order": row["order"],
                "owners": owners,
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
                }
            departments.append(doc)

        if departments and "departments" not in await self.mongo_db.list_collection_names():
            await self.mongo_db.departments.insert_many(departments)
            logger.info(f"Migrated {len(departments)} departments")

        return {"migrated": len(departments), "collection": "departments"}

    async def migrate_job_titles(self) -> Dict[str, Any]:
        """Migrate job titles with embedded levels and simplified schema."""
        logger.info("Starting job_titles migration...")

        # Get job titles
        job_title_query = """
        SELECT jt.job_title_id, jt.department_id, jt.company_id, jt.name, 
               jt.lower_case_name, jt.description, jt."order", jt.status,
               jt.created_by, jt.updated_by, jt.created_time, jt.updated_time
        FROM job_title jt
        ORDER BY jt.created_time
        """

        # Get job title levels
        job_level_query = """
        SELECT job_title_level_id, job_title_id, company_id, name, level, 
               lower_case_name, status, created_by, updated_by, created_time, updated_time
        FROM job_title_level
        ORDER BY job_title_id, level
        """

        job_title_rows = await self.pg_conn.fetch(job_title_query)
        job_level_rows = await self.pg_conn.fetch(job_level_query)

        # Group levels by job title
        levels_by_job_title = {}
        for level in job_level_rows:
            job_title_id = level["job_title_id"]
            if job_title_id not in levels_by_job_title:
                levels_by_job_title[job_title_id] = []

            levels_by_job_title[job_title_id].append(
                {
                    "job_title_level_id": str(level["job_title_level_id"]),
                    "name": level["name"],
                    "level": level["level"],
                    "lower_case_name": level["lower_case_name"],
                    "status": level["status"],
                    "created_by": str(level["created_by"]) if level["created_by"] else None,
                    "updated_by": str(level["updated_by"]) if level["updated_by"] else None,
                    "created_time": level["created_time"],
                    "updated_time": level["updated_time"],
                }
            )

        job_titles = []
        for row in job_title_rows:
            job_title_doc = {
                "job_title_id": str(row["job_title_id"]),
                "department_id": str(row["department_id"]) if row["department_id"] else None,
                "company_id": str(row["company_id"]) if row["company_id"] else None,
                "name": row["name"],
                "lower_case_name": row["lower_case_name"],
                "description": row["description"],
                "order": row["order"] or 0,
                "status": row["status"],
                "created_by": str(row["created_by"]) if row["created_by"] else None,
                "updated_by": str(row["updated_by"]) if row["updated_by"] else None,
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
                "levels": levels_by_job_title.get(row["job_title_id"], []),
            }

            job_titles.append(job_title_doc)

        if job_titles and "job_titles" not in await self.mongo_db.list_collection_names():
            await self.mongo_db.job_titles.insert_many(job_titles)
            logger.info(f"Migrated {len(job_titles)} job titles")

        logger.info(f"Prepared {len(job_titles)} job_titles for insertion.")
        return {"migrated": len(job_titles), "collection": "job_titles"}

    async def migrate_roles(self) -> Dict[str, Any]:
        """Migrate roles with embedded permissions and referenced policies (flattened schema)."""
        logger.info("Starting roles migration...")

        # Get roles
        role_query = """
        SELECT role_id, name, lower_case_name, description, company_id, status,
               created_by, updated_by, created_time, updated_time
        FROM "role"
        ORDER BY created_time
        """

        # Get permissions
        permission_query = """
        SELECT permission_id, company_id, description, action, scope, status,
               created_by, updated_by, created_time, updated_time
        FROM "permission"
        ORDER BY created_time
        """

        # Get role-permission mappings
        role_permission_query = """
        SELECT role_id, permission_id, status,
               created_by, updated_by, created_time, updated_time
        FROM role_permission
        WHERE status = 1
        ORDER BY role_id
        """

        # Get role-policy mappings
        role_policy_query = """
        SELECT role_id, policy_id, status
        FROM role_policies
        WHERE status = 1
        ORDER BY role_id
        """

        role_rows = await self.pg_conn.fetch(role_query)
        permission_rows = await self.pg_conn.fetch(permission_query)
        role_permission_rows = await self.pg_conn.fetch(role_permission_query)
        role_policy_rows = await self.pg_conn.fetch(role_policy_query)

        # Lookup permissions by id
        permissions_by_id = {p["permission_id"]: p for p in permission_rows}

        # Group permissions by role
        permissions_by_role = {}
        for rp in role_permission_rows:
            role_id = rp["role_id"]
            perm_id = rp["permission_id"]

            if role_id not in permissions_by_role:
                permissions_by_role[role_id] = []

            if perm_id in permissions_by_id:
                perm = permissions_by_id[perm_id]
                permissions_by_role[role_id].append(
                    {
                        "permission_id": str(perm["permission_id"]),
                        "description": perm["description"],
                        "action": perm["action"],
                        "scope": perm["scope"],
                        "status": perm["status"],
                        "created_by": str(perm["created_by"]) if perm["created_by"] else None,
                        "updated_by": str(perm["updated_by"]) if perm["updated_by"] else None,
                        "created_time": perm["created_time"],
                        "updated_time": perm["updated_time"],
                    }
                )

        # Group policies by role
        policies_by_role = {}
        for rp in role_policy_rows:
            role_id = rp["role_id"]
            if role_id not in policies_by_role:
                policies_by_role[role_id] = []
            policies_by_role[role_id].append(str(rp["policy_id"]))

        roles = []
        for row in role_rows:
            role_doc = {
                "role_id": str(row["role_id"]),
                "name": row["name"],
                "lower_case_name": row["lower_case_name"],
                "description": row["description"],
                "company_id": str(row["company_id"]) if row["company_id"] else None,
                "status": row["status"],
                "created_by": str(row["created_by"]) if row["created_by"] else None,
                "updated_by": str(row["updated_by"]) if row["updated_by"] else None,
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
                # Embedded permissions
                "permissions": permissions_by_role.get(row["role_id"], []),
                # Referenced policies (just store IDs)
                "policies": policies_by_role.get(row["role_id"], []),
            }

            roles.append(role_doc)

        if roles and "roles" not in await self.mongo_db.list_collection_names():
            await self.mongo_db.roles.insert_many(roles)
            logger.info(
                f"Migrated {len(roles)} roles with {sum(len(permissions_by_role.get(r['role_id'], [])) for r in role_rows)} permissions"
            )

        return {"migrated": len(roles), "permissions": len(permission_rows), "collection": "roles"}

    async def migrate_users(self) -> Dict[str, Any]:
        """Migrate users with flattened schema according to defined struct."""
        logger.info("Starting users migration...")

        # Get main user info
        user_query = """
        SELECT u.user_id, u.employee_code, u.company_id, u.first_name, 
               u.middle_name, u.last_name, u.name, u.unsigned_name, 
               u.primary_email, u.personal_email, u.username, u.password,
               u.gender, u.marital_status, u.education_level, u.date_of_birth,
               u.phone_number, u.current_address, u.home_town,
               u.lark_user_id, u.open_user_id,
               u.salary_amount, u.start_salary, u.start_onboard_at, u.contract_number,
               u."order", u.job_title_id, u.job_title_level_id,
               u.status, u.created_by, u.updated_by, u.created_time, u.updated_time,
               u.last_time_login, u.leader_user_id,
               u.thumb_avatar_link, u.icon_avatar_link,
               jt.name as job_title_name, jt.department_id, d.name as department_name,
               jtl.name as job_level_name, jtl.level as job_level_number,
               et.vi_name as employment_vi_name,
               leader.name as leader_name, leader.employee_code as leader_employee_code
        FROM "user" u
        LEFT JOIN job_title jt ON u.job_title_id = jt.job_title_id
        LEFT JOIN job_title_level jtl ON u.job_title_level_id = jtl.job_title_level_id
        LEFT JOIN department d ON jt.department_id = d.department_id
        LEFT JOIN employment_type et ON u.employment_type_id = et.employment_type_id
        LEFT JOIN "user" leader ON u.leader_user_id = leader.user_id
        ORDER BY u.created_time
        """

        # User-department mapping
        user_dept_query = """
        SELECT user_id, department_id
        FROM user_department
        WHERE status = 1
        ORDER BY user_id
        """

        # User-role mapping
        user_role_query = """
        SELECT user_id, role_id
        FROM user_role
        WHERE status = 1
        ORDER BY user_id
        """

        # User-policy mapping
        user_policy_query = """
        SELECT user_id, policy_id
        FROM user_policies
        WHERE status = 1
        ORDER BY user_id
        """

        user_rows = await self.pg_conn.fetch(user_query)
        user_dept_rows = await self.pg_conn.fetch(user_dept_query)
        user_role_rows = await self.pg_conn.fetch(user_role_query)
        user_policy_rows = await self.pg_conn.fetch(user_policy_query)

        # Group mappings
        departments_by_user = {}
        for row in user_dept_rows:
            departments_by_user.setdefault(row["user_id"], []).append(str(row["department_id"]))

        roles_by_user = {}
        for row in user_role_rows:
            roles_by_user.setdefault(row["user_id"], []).append(str(row["role_id"]))

        policies_by_user = {}
        for row in user_policy_rows:
            policies_by_user.setdefault(row["user_id"], []).append(str(row["policy_id"]))

        users = []
        for row in user_rows:
            user_doc = {
                "user_id": str(row["user_id"]),
                "employee_code": row["employee_code"],
                "company_id": str(row["company_id"]) if row["company_id"] else None,
                # Personal info
                "first_name": row["first_name"],
                "middle_name": row["middle_name"],
                "last_name": row["last_name"],
                "name": row["name"],
                "unsigned_name": row["unsigned_name"],
                "primary_email": row["primary_email"],
                "personal_email": row["personal_email"],
                "username": row["username"],
                "password": row["password"],
                "gender": row["gender"],
                "marital_status": row["marital_status"],
                "education_level": row["education_level"],
                "date_of_birth": row["date_of_birth"],
                # Contact
                "phone_number": row["phone_number"],
                "current_address": row["current_address"],
                "home_town": row["home_town"],
                # External IDs
                "lark_user_id": row["lark_user_id"],
                "open_user_id": row["open_user_id"],
                # Employment info
                "employment": {
                    "type_id": None,  # u.employment_type_id nếu cần
                    "type_name": row["employment_vi_name"],
                    "job_title": {
                        "id": str(row["job_title_id"]) if row["job_title_id"] else None,
                        "name": row["job_title_name"],
                        "department_id": str(row["department_id"]) if row["department_id"] else None,
                        "department_name": row["department_name"],
                    },
                    "job_level": {
                        "id": str(row["job_title_level_id"]) if row["job_title_level_id"] else None,
                        "name": row["job_level_name"],
                        "level": row["job_level_number"],
                    },
                },
                "salary_amount": float(row["salary_amount"]) if row["salary_amount"] else 0.0,
                "start_salary": row["start_salary"],
                "start_onboard_at": row["start_onboard_at"],
                "contract_number": row["contract_number"],
                "order": row["order"] or 0,
                # Leader
                "leader": {
                    "user_id": str(row["leader_user_id"]) if row["leader_user_id"] else None,
                    "name": row["leader_name"],
                    "employee_code": row["leader_employee_code"],
                },
                # Avatar
                "avatar": {
                    "thumb": row["thumb_avatar_link"],
                    "icon": row["icon_avatar_link"],
                },
                # System fields
                "last_time_login": row["last_time_login"],
                "status": row["status"],
                "created_by": str(row["created_by"]) if row["created_by"] else None,
                "updated_by": str(row["updated_by"]) if row["updated_by"] else None,
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
                # Relations
                "departments": departments_by_user.get(row["user_id"], []),
                "sub_departments": [],  # nếu sau này có
                "roles": roles_by_user.get(row["user_id"], []),
                "policies": policies_by_user.get(row["user_id"], []),
                # Quick access (denormalized)
                "quick_access": {
                    "primary_department_name": row["department_name"],
                    "primary_role_name": None,  # cần join thêm role nếu muốn
                    "manager_name": row["leader_name"],
                    "is_manager": False,  # tính toán thêm nếu muốn
                    "direct_reports_count": 0,
                },
            }

            users.append(user_doc)

        if users and "users" not in await self.mongo_db.list_collection_names():
            batch_size = 1000
            for i in range(0, len(users), batch_size):
                batch = users[i : i + batch_size]
                await self.mongo_db.users.insert_many(batch)
                logger.info(f"Inserted batch {i//batch_size + 1}: {len(batch)} users")

            logger.info(f"Migrated {len(users)} users")

        return {"migrated": len(users), "collection": "users"}

    async def migrate_user_events(self) -> Dict[str, Any]:
        """Migrate user_event table to separate collection."""
        logger.info("Starting user_events migration...")

        query = """
        SELECT ue.user_event_id, ue.employee_code, ue.body_event, ue.event_type,
               ue.status, ue.created_by, ue.updated_by, ue.created_time, ue.updated_time,
               u.company_id
        FROM user_event ue
        LEFT JOIN "user" u ON ue.employee_code = u.employee_code
        ORDER BY ue.created_time
        """

        rows = await self.pg_conn.fetch(query)
        events = []

        for row in rows:
            doc = {
                "company_id": row["company_id"],
                "employee_code": row["employee_code"],
                "user_event_id": row["user_event_id"],
                "body_event": row["body_event"],
                "event_type": row["event_type"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            events.append(doc)

        if events:
            await self.mongo_db.user_events.insert_many(events)
            logger.info(f"Migrated {len(events)} user events")

        return {"migrated": len(events), "collection": "user_events"}

    async def migrate_user_history(self) -> Dict[str, Any]:
        """Migrate user_history table to separate collection."""
        logger.info("Starting user_history migration...")

        query = """
        SELECT uh.history_id, uh.employee_code, uh.before_data, uh.after_data,
               uh.status, uh.created_by, uh.updated_by, uh.created_time, uh.updated_time,
               u.company_id
        FROM user_history uh
        LEFT JOIN "user" u ON uh.employee_code = u.employee_code
        ORDER BY uh.created_time
        """

        rows = await self.pg_conn.fetch(query)
        history = []

        for row in rows:
            # Simple change type detection
            change_type = "update"
            if not row["before_data"]:
                change_type = "create"
            elif not row["after_data"]:
                change_type = "delete"

            doc = {
                "company_id": row["company_id"],
                "employee_code": row["employee_code"],
                "history_id": row["history_id"],
                "before_data": row["before_data"],
                "after_data": row["after_data"],
                "change_type": change_type,
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            history.append(doc)

        if history:
            await self.mongo_db.user_history.insert_many(history)
            logger.info(f"Migrated {len(history)} user history records")

        return {"migrated": len(history), "collection": "user_history"}

    async def migrate_user_contacts(self) -> Dict[str, Any]:
        """Migrate contact_info table to user_contacts collection."""
        logger.info("Starting user_contacts migration...")

        query = """
        SELECT ci.contact_id, ci.company_id, ci.employee_code, ci.full_name,
               ci.email, ci.phone, ci.address, ci.relationship_type, ci.status,
               ci.created_by, ci.updated_by, ci.created_time, ci.updated_time
        FROM contact_info ci
        ORDER BY ci.created_time
        """

        rows = await self.pg_conn.fetch(query)
        contacts = []

        for row in rows:
            doc = {
                "company_id": row["company_id"],
                "employee_code": row["employee_code"],
                "contact_id": row["contact_id"],
                "full_name": row["full_name"],
                "email": row["email"],
                "phone": row["phone"],
                "address": row["address"],
                "relationship_type": row["relationship_type"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            contacts.append(doc)

        if contacts:
            await self.mongo_db.user_contacts.insert_many(contacts)
            logger.info(f"Migrated {len(contacts)} user contacts")

        return {"migrated": len(contacts), "collection": "user_contacts"}

    async def migrate_user_identifications(self) -> Dict[str, Any]:
        """Migrate user_identification table."""
        logger.info("Starting user_identifications migration...")

        query = """
        SELECT ui.user_identification_id, ui.company_id, ui.employee_code, ui.type,
               ui.issue_place, ui.front_image, ui.back_image, ui.identification_number,
               ui.issue_date, ui.status, ui.created_by, ui.updated_by, 
               ui.created_time, ui.updated_time
        FROM user_identification ui
        ORDER BY ui.created_time
        """

        rows = await self.pg_conn.fetch(query)
        identifications = []

        for row in rows:
            doc = {
                "company_id": row["company_id"],
                "employee_code": row["employee_code"],
                "user_identification_id": row["user_identification_id"],
                "type": row["type"],
                "issue_place": row["issue_place"],
                "front_image": row["front_image"],
                "back_image": row["back_image"],
                "identification_number": row["identification_number"],
                "issue_date": row["issue_date"],
                "status": row["status"],
                "created_by": row["created_by"],
                "updated_by": row["updated_by"],
                "created_time": row["created_time"],
                "updated_time": row["updated_time"],
            }
            identifications.append(doc)

        if identifications:
            await self.mongo_db.user_identifications.insert_many(identifications)
            logger.info(f"Migrated {len(identifications)} user identifications")

        return {"migrated": len(identifications), "collection": "user_identifications"}

    async def create_indexes(self):
        """Create all optimized indexes based on schema requirements."""
        logger.info("Creating MongoDB indexes...")

        index_operations = []

        # Companies indexes
        index_operations.extend(
            [
                ("companies", [("company_id", 1)], {"unique": True}),
                ("companies", [("tenant_key", 1)], {}),
            ]
        )

        # Users ultra-optimized indexes
        index_operations.extend(
            [
                ("users", [("user_id", 1)], {"unique": True}),
                ("users", [("employee_code", 1)], {"unique": True}),
                ("users", [("primary_email", 1)], {"unique": True}),
                ("users", [("unsigned_name", 1)], {"unique": True, "sparse": True}),
                ("users", [("company_id", 1), ("employment.job_title.department_id", 1), ("system.status", 1)], {}),
                ("users", [("company_id", 1), ("leader.user_id", 1)], {}),
                ("users", [("employment.job_title.id", 1), ("status", 1)], {}),
                ("users", [("company_id", 1), ("created_time", -1)], {}),
                ("users", [("company_id", 1), ("roles", 1), ("status", 1)], {}),
            ]
        )

        # Departments repository-optimized indexes
        index_operations.extend(
            [
                ("departments", [("department_id", 1)], {"unique": True}),
                ("departments", [("company_id", 1), ("status", 1)], {}),
                ("departments", [("company_id", 1), ("lower_case_name", 1)], {"unique": True}),
                ("departments", [("owners", 1)], {}),
            ]
        )

        # Job titles repository-pattern optimized indexes
        index_operations.extend(
            [
                ("job_titles", [("job_title_id", 1)], {"unique": True}),
                ("job_titles", [("company_id", 1), ("department_id", 1), ("status", 1)], {}),
                ("job_titles", [("levels.job_title_level_id", 1)], {}),
                ("job_titles", [("levels.lower_case_name", 1)], {}),
            ]
        )

        # Roles RBAC-optimized indexes
        index_operations.extend(
            [
                ("roles", [("role_id", 1)], {"unique": True}),
                ("roles", [("company_id", 1), ("lower_case_name", 1)], {"unique": True}),
                ("roles", [("permissions.permission_id", 1)], {}),
                ("roles", [("permissions.action", 1), ("permissions.scope", 1)], {}),
                ("roles", [("company_id", 1), ("permissions.action", 1)], {})
            ]
        )

        # Policies indexes
        index_operations.extend(
            [
                ("policies", [("policy_id", 1)], {"unique": True}),
                ("policies", [("company_id", 1), ("name", 1)], {"unique": True}),
                ("policies", [("company_id", 1), ("policy_type", 1), ("status", 1)], {}),
                ("policies", [("company_id", 1), ("status", 1)], {}),
            ]
        )

        # Employment types indexes
        index_operations.extend(
            [
                ("employment_types", [("employment_type_id", 1)], {"unique": True}),
                ("employment_types", [("company_id", 1), ("status", 1)], {}),
            ]
        )

        # Execute index creation
        for collection_name, index_spec, options in index_operations:
            try:
                await self.mongo_db[collection_name].create_index(index_spec, **options)
                logger.info(f"Created index on {collection_name}: {index_spec}")
            except Exception as e:
                logger.warning(f"Index creation failed for {collection_name} {index_spec}: {e}")


    async def run_full_migration(self) -> Dict[str, Any]:
        """Execute complete migration from PostgreSQL to MongoDB."""
        logger.info("Starting full migration from PostgreSQL to MongoDB...")
        start_time = datetime.utcnow()

        try:
            # Migration order is important due to dependencies
            migration_steps = [
                ("companies", self.migrate_companies),
                ("employment_types", self.migrate_employment_types),
                ("policies", self.migrate_policies),
                ("departments", self.migrate_departments),
                ("job_titles", self.migrate_job_titles),
                ("roles", self.migrate_roles),
                ("users", self.migrate_users)
            ]

            migration_results = {}

            for step_name, migration_func in migration_steps:
                try:
                    logger.info(f"Starting {step_name} migration...")
                    result = await migration_func()
                    migration_results[step_name] = result
                    logger.info(f"Completed {step_name} migration: {result}")
                except Exception as e:
                    logger.error(f"Failed to migrate {step_name}: {e}")
                    migration_results[step_name] = {"error": str(e), "migrated": 0}

            # Create indexes after all data is migrated
            await self.create_indexes()

            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            # Calculate totals
            total_migrated = sum(result.get("migrated", 0) for result in migration_results.values())

            summary = {
                "status": "completed",
                "start_time": start_time,
                "end_time": end_time,
                "duration_seconds": duration,
                "total_records_migrated": total_migrated,
                "collections_migrated": len(migration_results),
                "results": migration_results,
            }

            logger.info(f"Migration completed successfully in {duration:.2f} seconds")
            logger.info(f"Total records migrated: {total_migrated}")

            return summary

        except Exception as e:
            logger.error(f"Migration failed: {e}")
            raise


async def main():
    """Main migration function."""
    import argparse

    parser = argparse.ArgumentParser(description="Migrate HR data from PostgreSQL to MongoDB")
    parser.add_argument("--drop-existing", action="store_true", help="Drop existing collections before migration")

    args = parser.parse_args()

    async with SQLToMongoMigrator() as migrator:
        # if args.drop_existing:
        #     logger.info("Dropping existing collections...")
        #     collections = await migrator.mongo_db.list_collection_names()
        #     for collection in collections:
        #         await migrator.mongo_db[collection].drop()
        #         logger.info(f"Dropped collection: {collection}")

        # Run migration
        result = await migrator.run_full_migration()

        # Print summary
        print("\n" + "=" * 50)
        print("MIGRATION SUMMARY")
        print("=" * 50)
        print(f"Status: {result['status']}")
        print(f"Duration: {result['duration_seconds']:.2f} seconds")
        print(f"Total records: {result['total_records_migrated']}")
        print(f"Collections: {result['collections_migrated']}")

        for collection, stats in result["results"].items():
            if "error" in stats:
                print(f"❌ {collection}: ERROR - {stats['error']}")
            else:
                print(f"✅ {collection}: {stats['migrated']} records")

        print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
