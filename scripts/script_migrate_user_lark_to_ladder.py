#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 13/06/2025
"""

import json


def migrate_user_lark_to_ladder():
    department_data_lark = json.load(open("department_data_lark.json"))
    user_data_lark = json.load(open("user_data_lark.json"))

    mapping_user_id_with_data = {}
    mapping_department_id_with_data = {}

    department_id_level_c = []

    for department_data in department_data_lark:
        department_id = department_data["department_id"]
        mapping_department_id_with_data[department_id] = department_data
        if department_data["name"] in ["BOM", "BOD"]:
            department_id_level_c.append(department_id)

    mapping_user_c_level = {}
    for user_data in user_data_lark:
        open_id = user_data["open_id"]
        mapping_user_id_with_data[open_id] = user_data
        department_ids = user_data["department_ids"]
        for department_id in department_ids:
            if department_id in department_id_level_c:
                mapping_user_c_level[open_id] = user_data
                break

    result_data = {}

    for user_data in user_data_lark:
        open_id = user_data["open_id"]
        department_orders = user_data["orders"]
        leader_id = user_data.get("leader_user_id") if open_id not in mapping_user_c_level else None

        for department_order in department_orders:
            department_id = department_order["department_id"]
            is_primary_dept = department_order["is_primary_dept"]
            user_id = user_data["user_id"]
            if not is_primary_dept:
                continue

            department_data = mapping_department_id_with_data.get(department_id)
            if department_data:
                leaders = department_data.get("leaders")
                main_personal_in_charge = False
                personal_in_charge = False

                if leaders:
                    for leader in leaders:
                        if leader["leaderID"] == user_id:
                            if leader["leaderType"] == 1:
                                main_personal_in_charge = True
                                break
                            if leader["leaderType"] == 2:
                                personal_in_charge = True
                                break

                department_name = department_data["name"]
                leader_data = mapping_user_id_with_data.get(leader_id)
                if leader_data:
                    leader_name = leader_data["name"]
                    result_data[open_id] = {
                        "leader_id": leader_id,
                        "leader_name": leader_name,
                        "department_id": department_id,
                        "department_name": department_name,
                        "open_id": open_id,
                        "user_name": user_data["name"],
                        "main_personal_in_charge": main_personal_in_charge,
                        "personal_in_charge": personal_in_charge,
                    }

    r = {}
    
    user_department_data = {}
    for _, user_data in result_data.items():
        department_id = user_data["department_id"]
        department_data = mapping_department_id_with_data[department_id]
        department_name = department_data["name"]
        if department_name not in r:
            r[department_name] = {
                "c_level": [],
                "leader": [],
                "sub_leader": [],
                "member": [],
            }
        if department_data["name"] in ["BOM", "BOD"]:
            continue
        leader_id = user_data["leader_id"]
        open_id = user_data["open_id"]
        main_personal_in_charge = user_data["main_personal_in_charge"]
        personal_in_charge = user_data["personal_in_charge"]
        

        if leader_id in mapping_user_c_level:
            if mapping_user_id_with_data.get(leader_id).get("name") not in r[department_name]["c_level"]:
                r[department_name]["c_level"].append(mapping_user_id_with_data.get(leader_id).get("name"))
        if open_id in mapping_user_c_level:
            continue
        if not mapping_user_id_with_data.get(open_id):
            continue
        if main_personal_in_charge:
            if mapping_user_id_with_data.get(open_id, {}).get("name") not in r[department_name]["leader"]:
                r[department_name]["leader"].append(mapping_user_id_with_data.get(open_id, {}).get("name"))
        if personal_in_charge:
            if mapping_user_id_with_data.get(open_id).get("name") not in r[department_name]["sub_leader"]:
                r[department_name]["sub_leader"].append(mapping_user_id_with_data.get(open_id).get("name"))
        if not main_personal_in_charge and not personal_in_charge:
            if mapping_user_id_with_data.get(open_id).get("name") not in r[department_name]["member"]:
                r[department_name]["member"].append(mapping_user_id_with_data.get(open_id).get("name"))

    print(r)


if __name__ == "__main__":
    migrate_user_lark_to_ladder()
