#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 19/08/2024
"""

import asyncio
import sys

from mobio.libs.logging import MobioLogging

from src.models.mongo.setting_model import SettingModel


async def get_config_in_setting():

    setting_model: SettingModel = SettingModel()

    setting_detail = await setting_model.find_one({})
    MobioLogging().info(f"get_config_in_setting :: setting_detail :: {setting_detail}")


if __name__ == "__main__":
    asyncio.run(
        get_config_in_setting()
    )
