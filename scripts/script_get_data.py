#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
    Author: Tungdd
    Company: MobioVN
    Date created: 14/06/2025
"""

import asyncio

from bson import ObjectId
from src.common.common import EvaluateKey
from src.common.constants import ConstantTypeOfReview
from src.controllers.evaluate_controller import <PERSON><PERSON>ateController
from src.models.mongo.evaluate_model import EvaluateModel
from src.models.mongo.evaluate_period_model import EvaluatePeriodModel
from src.models.postgres.base_model import UserModel
from src.repositories.job_title_level_repository import JobTitleLevelRepository
from src.repositories.job_title_repository import JobTitleRepository
from src.repositories.users_repository import UserRepository
from sqlalchemy.orm import Session

from configs.database import get_db

async def x():
    x1 = await EvaluatePeriodModel().find({})
    for i in x1:
        
        print(i)
        
async def y():
    session_db: Session = next(get_db())
    users = session_db.query(UserModel).all()
    
    lst_user_not_active = []
    for user in users:
        print(user.name)
        departments = user.departments
        if not departments:
            lst_user_not_active.append(user.user_id)
            print("user_name_deleted: ", user.name)
            continue
        # print("--------------------------------")
        # for department in departments:
        #     print(department.department_id)
        #     print(department.name)
        #     print(department.job_title_id)
        #     print(department.job_title_level_id)
        #     print(department.leader_user_id)
        #     print(department.created_time)
        #     print(department.updated_time)
        #     print(department.created_by)
        # print("--------------------------------")
    # Delete 
    await EvaluateModel().update_by_set({"user_id": {"$in": lst_user_not_active}}, {"company_id": None, "evaluate_period_id": None})
    

async def a(evaluates, user_repo, job_title_repo, job_title_level_repo):
    data_return = []

    for evaluate in evaluates:
        user_id = evaluate.get(EvaluateKey.USER_ID)
        evaluate_id = str(evaluate.get(EvaluateKey.ID))
        evaluate_period_id = evaluate.get(EvaluateKey.EVALUATE_PERIOD_ID)
        time_eval_of_users = evaluate.get(EvaluateKey.TIME_EVAL_OF_USERS, [])
        status = evaluate.get(EvaluateKey.STATUS)
        is_draft = evaluate.get(EvaluateKey.IS_DRAFT, False)
        after_job_title_level_id = evaluate.get(EvaluateKey.AFTER_JOB_TITLE_LEVEL_ID)
        before_job_title_level_id = evaluate.get(EvaluateKey.BEFORE_JOB_TITLE_LEVEL_ID)

        user = await user_repo.get_user_by_id(user_id)
        leader = await user_repo.get_user_by_id(user.leader_user_id)
        job_title = await job_title_repo.get_job_title_by_id(user.job_title_id)
        after_job_title_level = await job_title_level_repo.get_job_title_level_by_id(after_job_title_level_id)
        before_job_title_level = await job_title_level_repo.get_job_title_level_by_id(before_job_title_level_id)
        job_title_level_status = evaluate.get(EvaluateKey.JOB_TITLE_LEVEL_STATUS)
        print(user.name)

        # Required field evaluate
        evaluate_data = {
            EvaluateKey.ID: evaluate_id,
            EvaluateKey.EVALUATE_PERIOD_ID: evaluate_period_id,
            EvaluateKey.USER_ID: user_id,
            EvaluateKey.USER_NAME: user.name,
            EvaluateKey.USER_AVATAR: user.thumb_avatar_link,
            EvaluateKey.STATUS: status,
            EvaluateKey.IS_DRAFT: is_draft,
            EvaluateKey.DEPARTMENT_ID: user.departments[0].department_id,
            EvaluateKey.DEPARTMENT_NAME: user.departments[0].name,
            EvaluateKey.JOB_TITLE: job_title.name,
            EvaluateKey.AFTER_JOB_TITLE_LEVEL: after_job_title_level.name if after_job_title_level else "",
            EvaluateKey.BEFORE_JOB_TITLE_LEVEL: before_job_title_level.name if before_job_title_level else "",
            EvaluateKey.TIME_EVAL_OF_USERS: time_eval_of_users,
            EvaluateKey.JOB_TITLE_LEVEL_STATUS: job_title_level_status,
            EvaluateKey.TYPE_OF_REVIEW: evaluate.get(EvaluateKey.TYPE_OF_REVIEW, ConstantTypeOfReview.PERIODIC),
            "evaluate_start_time": (
                evaluate.get(EvaluateKey.START_TIME).strftime("%Y-%m-%dT%H:%MZ")
                if evaluate.get(EvaluateKey.START_TIME)
                else None
            ),
            "evaluate_end_time": (
                evaluate.get(EvaluateKey.END_TIME).strftime("%Y-%m-%dT%H:%MZ")
                if evaluate.get(EvaluateKey.END_TIME)
                else None
            ),
        }

        if leader:
            evaluate_data.update(
                {
                    EvaluateKey.LEADER_ID: leader.user_id,
                    EvaluateKey.LEADER_NAME: leader.name,
                    EvaluateKey.LEADER_AVATAR: leader.thumb_avatar_link,
                }
            )
        data_return.append(evaluate_data)
    return data_return
async def z():
    evaluate_model: EvaluateModel = EvaluateModel()
    evaluates = await evaluate_model.find({"evaluate_period_id": {"$in": [ObjectId("678476fa1ff286fe77af5e70")]}})
    session: Session = next(get_db())
    user_repo: UserRepository = UserRepository(session)
    job_title_repo: JobTitleRepository = JobTitleRepository(session)
    job_title_level_repo: JobTitleLevelRepository = JobTitleLevelRepository(session)
    data_return = await a(evaluates, user_repo, job_title_repo, job_title_level_repo)    
    print(data_return)
    
async def w():
    evaluate_model: EvaluateModel = EvaluateModel()
    evaluates = await evaluate_model.find({})
    for evaluate in evaluates:
        evaluate_period_id = evaluate.get(EvaluateKey.EVALUATE_PERIOD_ID)
        if evaluate_period_id:
            evaluate_model.update_by_set({"_id": evaluate.get("_id")}, {"evaluate_period_id": ObjectId(evaluate_period_id)})

if __name__ == "__main__":
    asyncio.run(w())