#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Author: Tungdd
Company: MobioVN
Date created: 12/06/2025
"""

import asyncio
import json
from mobio.libs.logging import MobioLogging

from configs.database import get_db
from src.common.choices import DepartmentChoice
from src.common.constants import ConstantStatus
from src.libs.lark_docs_sdk.oapi.object.department import LarkObjectDepartmentApi
from src.libs.lark_docs_sdk.oapi.object.user import LarkObjectUserApi
from src.utils import utf8_to_ascii


async def get_all_info_user_lark():
    departments = LarkObjectDepartmentApi().get_all_department_can_access()
    # insert information department
    department_mapping = {}
    department_order = 0
    child_departments = []
    session_db = next(get_db())
    list_activating_user_id = set()
    
    user_data_lark = []
    department_data_lark = []
    for department_information in departments:
        MobioLogging().info(f"sync_data_from_lark::department_information::{department_information}")

        department_id = department_information["department_id"]
        department_order += 1
        department_mapping[department_id] = {**department_information, "order": department_order}

        if int(department_information["parent_department_id"]):
            child_departments.append(department_information)
            continue

        display = 1
        if department_information["name"].lower() in [DepartmentChoice.BOD.value]:
            display = 0

        status = 1 if not department_information["status"]["is_deleted"] else 0
        lower_case_name = department_information["name"].lower()
        owners = department_information.get("owners", [])
        MobioLogging().info(f"sync_data_from_lark::owners::{owners}")
        department_data_lark.append(department_information)

        users = LarkObjectUserApi().get_list_all_users_of_department(department_id=department_id)
        department_mapping[department_id]["users"] = users
        if not users:
            continue

        for user_information in users:
            user_information_name = user_information["name"]
            MobioLogging().info(f"sync_data_from_lark::user_name::{user_information_name}")

            lark_user_id = user_information["user_id"]
            user_status = (
                ConstantStatus.ACTIVE if user_information["status"]["is_activated"] else ConstantStatus.DEACTIVE
            )
            unsigned_name = utf8_to_ascii(user_information["name"]).lower()
            thumb_avatar_link = user_information["avatar"]["avatar_origin"]
            if user_status == ConstantStatus.ACTIVE:
                list_activating_user_id.add(lark_user_id)
            is_tenant_manager = user_information["is_tenant_manager"]
            # Assign role
            # user_information_roles = [user_role]
            # if is_tenant_manager:
            #     user_information_roles.append(admin_role)
            # if user_information_name == "Nguyễn Việt Ánh" and not user_information["job_title"]:
            #     user_information["job_title"] = "Lập trình viên"
            user_data_lark.append(user_information)

            MobioLogging().info(f"sync_data_from_lark::user_information_roles::{user_information['job_title']}")

    # # replace leader_user_id of user
    # users_in_system = session_db.query(UserModel).all()
    # bod_department = session_db.query(DepartmentModel).filter_by(lower_case_name="bod").first()
    # for user in users_in_system:
    #     # Update user leader id
    #     if user.leader_user_id:
    #         leader = session_db.query(UserModel).filter_by(open_user_id=user.leader_user_id).first()
    #         user.leader_user_id = leader.user_id if leader else None
    #     if bod_department in user.departments:
    #         user.leader_user_id = None

    #     # # Check user deleted in lark
    #     if user.lark_user_id not in list_activating_user_id:
    #         MobioLogging().info(
    #             f"sync_data_from_lark::user_name::{user.name}::user_lark_id::{user.lark_user_id} is deleted"
    #         )
    #         user.status = ConstantStatus.DELETED

    # session_db.commit()

    # # Update owners in department
    # list_department_system = session_db.query(DepartmentModel).all()
    # for department_system in list_department_system:
    #     department_in_lark = department_mapping[department_system.lark_department_id]
    #     leaders = department_in_lark.get("leaders", [])
    #     if not leaders:
    #         continue
    #     owners = []
    #     for leader in leaders:
    #         lark_leader_user_id = leader.get("leaderID")
    #         user = session_db.query(UserModel).filter_by(lark_user_id=lark_leader_user_id).first()
    #         user.roles.append(leader_role)
    #         if user_role in user.roles:
    #             user.roles.remove(user_role)
    #         owners.append(user.user_id)
    #     department_system.owners = owners

    with open("user_data_lark_test.json", "w") as f:
        json.dump(user_data_lark, f, ensure_ascii=False, indent=4)

    with open("department_data_lark_test.json", "w") as f:
        json.dump(department_data_lark, f, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    asyncio.run(get_all_info_user_lark())
