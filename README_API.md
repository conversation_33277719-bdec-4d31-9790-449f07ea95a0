# HR User Management API

FastAPI application for creating and managing users in the HR system with MongoDB integration.

## Features

- **User Creation API**: Submit user data with validation and save to MongoDB
- **MongoDB Schema**: Optimized schema following the `mongodb.md` specifications
- **Manual vs System Users**: Distinguish between manually entered and system-generated users
- **Data Validation**: Comprehensive validation using Pydantic models
- **Denormalized Data**: Optimized for read performance with embedded references
- **Audit Trail**: User events tracking for compliance

## Requirements

- Python 3.8+
- MongoDB 4.4+
- Dependencies listed in `requirements.txt`

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure MongoDB**:
   ```bash
   # Set environment variables (optional)
   export MONGODB_URL="mongodb://localhost:27017"
   export DATABASE_NAME="hr_system"
   ```

3. **Start the API Server**:
   ```bash
   python start_api.py
   # or
   python app.py
   # or
   uvicorn app:app --host 0.0.0.0 --port 8000 --reload
   ```

4. **Access the Application**:
   - **User Input Form**: http://localhost:8000/ or http://localhost:8000/form
   - **API Documentation**: http://localhost:8000/docs
   - **Health Check**: http://localhost:8000/api/health
   - **API Root**: http://localhost:8000/api

## API Endpoints

### Core Endpoints

- `GET /` - Serve the user input form (HTML)
- `GET /form` - Alternative endpoint for the user input form
- `GET /api` - API root endpoint for health check
- `GET /api/health` - Detailed health check with database status
- `POST /api/users` - Create a new user
- `GET /api/users/{user_id}` - Get user by ID
- `GET /api/users` - List users with pagination and filtering

### Data Endpoints

- `GET /api/leaders` - Get list of leaders for selection
- `GET /api/departments` - Get list of departments
- `GET /api/job-titles` - Get job titles (optionally filtered by department)

## User Creation API

### Request Format

```json
{
  "first_name": "Nguyễn",
  "middle_name": "Văn",
  "last_name": "An",
  "employee_code": "EMP001", // Optional - auto-generated if not provided
  "primary_email": "<EMAIL>",
  "phone_number": "**********",
  "gender": 0, // 0=Male, 1=Female, 2=Other
  "date_of_birth": "1990-01-01",
  "current_address": "123 Main St, Hanoi",
  "personal_email": "<EMAIL>",
  "home_town": "Hanoi",
  "education_level": "Đại học",
  "marital_status": 0, // 0=Single, 1=Married, 2=Divorced, 3=Widowed
  "leader_user_id": "usr_12345", // Optional
  "department_id": "dept_001",
  "job_title_id": "jt_001",
  "job_title_level_id": "jtl_001", // Optional
  "user_entry_type": "manual", // "manual" or "system"
  "company_id": "comp_001"
}
```

### Response Format

```json
{
  "success": true,
  "message": "User created successfully",
  "user_id": "usr_abcd1234",
  "employee_code": "EMP123456",
  "data": {
    "name": "Nguyễn Văn An",
    "email": "<EMAIL>",
    "department": "Phòng Công nghệ",
    "job_title": "Kỹ sư phần mềm",
    "entry_type": "manual"
  }
}
```

## MongoDB Schema

The API creates documents following the optimized schema in `mongodb.md`:

### Key Features

- **Denormalized References**: Department names, job title names embedded for performance
- **Entry Metadata**: Tracks whether user was manually entered or system-generated
- **Audit Fields**: Complete audit trail with created_by, updated_by, timestamps
- **Search Optimization**: Unsigned names and searchable text fields
- **Performance Indexes**: Compound indexes for common query patterns

### Sample Document Structure

```javascript
{
  "user_id": "usr_abcd1234",
  "employee_code": "EMP123456",
  "company_id": "comp_001",
  "name": "Nguyễn Văn An",
  "unsigned_name": "nguyen van an",
  "primary_email": "<EMAIL>",
  "employment": {
    "job_title": {
      "id": "jt_001",
      "name": "Kỹ sư phần mềm",
      "department_id": "dept_001",
      "department_name": "Phòng Công nghệ"
    }
  },
  "leader": {
    "user_id": "usr_12345",
    "name": "Trần Văn B",
    "employee_code": "EMP001"
  },
  "entry_metadata": {
    "entry_type": "manual",
    "entry_source": "admin_form",
    "created_by_type": "human"
  },
  "status": 1,
  "created_time": "2024-01-01T00:00:00Z"
}
```

## Web Interface

### Accessing the Form

The user input form is now served directly from the API at:
- **Primary URL**: http://localhost:8000/
- **Alternative URL**: http://localhost:8000/form

### Form Features

The web interface provides a user-friendly form that:

- **Dynamic Data Loading**: Automatically loads leaders, departments, and job titles from API
- **Client-side Validation**: Real-time validation with user-friendly error messages
- **Searchable Dropdowns**: Filter leaders, departments, and job titles with search
- **Department Filtering**: Job titles automatically filter based on selected department
- **Entry Type Selection**: Toggle between "Manual Entry" and "System Generated" users
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Feedback**: Loading states, success messages, and error handling
- **API Integration**: Seamless integration with backend API endpoints

### Usage Flow

1. **Select Entry Type**: Choose between manual or system-generated user
2. **Fill Basic Information**: Enter required personal details
3. **Select Leader**: Optional - choose from searchable list of managers
4. **Select Department**: Required - choose from available departments
5. **Select Job Title**: Required - automatically filtered by department
6. **Select Job Level**: Optional - appears after job title selection
7. **Add Additional Info**: Optional fields for complete profile
8. **Submit**: Form validates and submits to API with real-time feedback

## Error Handling

The API provides comprehensive error handling:

- **Validation Errors**: Field-level validation with detailed messages
- **Duplicate Data**: Specific errors for duplicate emails/employee codes
- **Reference Validation**: Checks for valid departments, job titles, leaders
- **Database Errors**: Graceful handling of MongoDB connection issues

## Performance Considerations

- **Indexes**: Optimized indexes for common query patterns
- **Denormalization**: Critical reference data embedded to avoid joins
- **Validation**: Server-side validation to ensure data integrity
- **Connection Pooling**: Efficient MongoDB connection management
- **Async Operations**: Non-blocking database operations

## Development

### Running in Development

```bash
python start_api.py
```

### Environment Variables

```bash
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=hr_system
HOST=0.0.0.0
PORT=8000
RELOAD=true
```

### Testing

Access the interactive API documentation at http://localhost:8000/docs to test endpoints.

## Production Deployment

1. Set production environment variables
2. Configure MongoDB connection with authentication
3. Use a production WSGI server like Gunicorn
4. Set up proper logging and monitoring
5. Configure CORS for your domain
6. Enable HTTPS

```bash
# Production example
gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```